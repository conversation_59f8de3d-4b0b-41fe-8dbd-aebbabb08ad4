import 'package:dextrip_app/pages/trades_page.dart';
import 'package:dextrip_app/widgets/gradient_button.dart';
import 'package:flutter/material.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'pages/coin_page.dart';
import 'pages/bot_page.dart';
import 'pages/recommendation_page.dart';
import 'pages/wallet_page.dart';
import 'services/supabase_auth_service.dart';
import 'package:dextrip_app/widgets/auth_bottom_sheet.dart';

import 'theme/app_theme.dart';

class MyHomePage extends StatefulWidget {
  const MyHomePage({
    super.key,
    required this.title,
    required this.onThemeToggle,
    required this.isDarkMode,
  });
  final String title;
  final VoidCallback onThemeToggle;
  final bool isDarkMode;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final SupabaseAuthService _authService = SupabaseAuthService();
  bool _isSignedIn = false; // Track user sign-in status

  // List of pages for signed-in users
  late final List<Widget> _pages;
  // Track the current page index (0: Coin, 1: Bots, 3: Wallet)
  int _currentPageIndex = 0;

  @override
  void initState() {
    super.initState();
    _checkAuthState();
    _setupAuthListener();
    _initializePages();
  }

  void _initializePages() {
    _pages = [
      CoinPage(
        onThemeToggle: widget.onThemeToggle,
        isDarkMode: widget.isDarkMode,
        isSignedIn: _isSignedIn,
        onSignIn: _handleSignIn,
      ),
      const BotPage(),
      const RecommendationPage(),
      const TradesPage(),
      const WalletPage(),
    ];
  }

  void _checkAuthState() {
    setState(() {
      _isSignedIn = _authService.isAuthenticated;
    });
  }

  void _handleSignIn() {
    setState(() {
      _isSignedIn = true;
    });
    _initializePages(); // Reinitialize pages with new auth state

    // Navigate to the Coins page (index 0) after successful sign-in
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _currentPageIndex = 0; // Navigate to Coins page
        });
      }
    });
  }

  void _setupAuthListener() {
    try {
      Supabase.instance.client.auth.onAuthStateChange.listen((data) {
        final AuthChangeEvent event = data.event;
        if (event == AuthChangeEvent.signedIn) {
          setState(() {
            _isSignedIn = true;
          });
          _initializePages(); // Reinitialize pages with new auth state
        } else if (event == AuthChangeEvent.signedOut) {
          setState(() {
            _isSignedIn = false;
          });
          _initializePages(); // Reinitialize pages with new auth state
        }
      });
    } catch (e) {
      debugPrint('Auth listener setup failed (offline mode): $e');
      // Continue without auth listener in offline mode
    }
  }

  @override
  Widget build(BuildContext context) {
    // Always show the main app structure
    return Scaffold(
      body: _pages[_currentPageIndex],
      bottomNavigationBar: !_isSignedIn
          ? _buildGetStartedFooter()
          : SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 8,
                ),
                child: GNav(
                  gap: 8,
                  activeColor: Theme.of(context).colorScheme.primary,
                  iconSize: 24,
                  duration: const Duration(milliseconds: 250),
                  tabBackgroundColor: Theme.of(
                    context,
                  ).colorScheme.primary.withValues(alpha: 0.1),
                  color:
                      Theme.of(
                        context,
                      ).textTheme.bodySmall?.color?.withValues(alpha: 0.6) ??
                      Colors.grey[600]!,
                  tabs: [
                    GButton(
                      icon: LucideIcons.circleDot,
                      // text: 'COINS',
                      iconSize: 22,
                      padding: const EdgeInsets.all(12),
                      iconActiveColor: Colors.white,
                      iconColor: _currentPageIndex == 0
                          ? Colors.white
                          : Theme.of(context).textTheme.bodySmall?.color
                                    ?.withValues(alpha: 0.6) ??
                                Colors.grey[600]!,
                      backgroundColor: _currentPageIndex == 0
                          ? AppTheme.primaryColor
                          : Colors.transparent,
                      backgroundGradient: _currentPageIndex == 0
                          ? AppTheme.primaryGradient
                          : null,
                      textColor: _currentPageIndex == 0 ? Colors.white : null,
                      active: _currentPageIndex == 0,
                    ),
                    GButton(
                      icon: LucideIcons.bot,
                      iconSize: 22,
                      padding: const EdgeInsets.all(12),
                      iconActiveColor: Colors.white,
                      iconColor: _currentPageIndex == 1
                          ? Colors.white
                          : Theme.of(context).textTheme.bodySmall?.color
                                    ?.withValues(alpha: 0.6) ??
                                Colors.grey[600]!,
                      backgroundColor: _currentPageIndex == 1
                          ? AppTheme.primaryColor
                          : Colors.transparent,
                      backgroundGradient: _currentPageIndex == 1
                          ? AppTheme.primaryGradient
                          : null,
                      textColor: _currentPageIndex == 1 ? Colors.white : null,
                      active: _currentPageIndex == 1,
                    ),
                    GButton(
                      icon: LucideIcons.star,
                      iconSize: 22,
                      padding: const EdgeInsets.all(12),
                      iconActiveColor: Colors.white,
                      iconColor: _currentPageIndex == 2
                          ? Colors.white
                          : Theme.of(context).textTheme.bodySmall?.color
                                    ?.withValues(alpha: 0.6) ??
                                Colors.grey[600]!,
                      backgroundColor: _currentPageIndex == 2
                          ? AppTheme.primaryColor
                          : Colors.transparent,
                      backgroundGradient: _currentPageIndex == 2
                          ? AppTheme.primaryGradient
                          : null,
                      textColor: _currentPageIndex == 2 ? Colors.white : null,
                      active: _currentPageIndex == 2,
                    ),

                    GButton(
                      icon: LucideIcons.arrowUpDown,
                      iconSize: 22,
                      padding: const EdgeInsets.all(12),
                      iconActiveColor: Colors.white,
                      iconColor: _currentPageIndex == 3
                          ? Colors.white
                          : Theme.of(context).textTheme.bodySmall?.color
                                    ?.withValues(alpha: 0.6) ??
                                Colors.grey[600]!,
                      backgroundColor: _currentPageIndex == 3
                          ? AppTheme.primaryColor
                          : Colors.transparent,
                      backgroundGradient: _currentPageIndex == 3
                          ? AppTheme.primaryGradient
                          : null,
                      textColor: _currentPageIndex == 3 ? Colors.white : null,
                      active: _currentPageIndex == 3,
                    ),
                    GButton(
                      icon: LucideIcons.wallet,
                      iconSize: 22,
                      padding: const EdgeInsets.all(12),
                      iconActiveColor: Colors.white,
                      iconColor: _currentPageIndex == 4
                          ? Colors.white
                          : Theme.of(context).textTheme.bodySmall?.color
                                    ?.withValues(alpha: 0.6) ??
                                Colors.grey[600]!,
                      backgroundColor: _currentPageIndex == 4
                          ? AppTheme.primaryColor
                          : Colors.transparent,
                      backgroundGradient: _currentPageIndex == 4
                          ? AppTheme.primaryGradient
                          : null,
                      textColor: _currentPageIndex == 4 ? Colors.white : null,
                      active: _currentPageIndex == 4,
                    ),
                  ],
                  selectedIndex: _currentPageIndex,
                  onTabChange: (index) {
                    setState(() {
                      _currentPageIndex = index;
                    });
                  },
                ),
              ),
            ),
    );
  }

  Widget _buildGetStartedFooter() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
        child: GradientButton(
          text: 'Get Started',
          onPressed: _showAuthBottomSheet,
          // icon: LucideIcons.arrowRight,
        ),
      ),
    );
  }

  void _showAuthBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AuthBottomSheet(
        onSuccess: () {
          _handleSignIn();
        },
      ),
    );
  }
}
