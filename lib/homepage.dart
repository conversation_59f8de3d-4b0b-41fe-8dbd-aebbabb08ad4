import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'pages/responsive_coin_page.dart';
import 'pages/bot_page.dart';
import 'pages/recommendation_page.dart';
import 'pages/trades_page.dart';
import 'pages/wallet_page.dart';
import 'services/supabase_auth_service.dart';
import 'widgets/responsive_home_layout.dart';

class MyHomePage extends StatefulWidget {
  const MyHomePage({
    super.key,
    required this.title,
    required this.onThemeToggle,
    required this.isDarkMode,
  });
  final String title;
  final VoidCallback onThemeToggle;
  final bool isDarkMode;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final SupabaseAuthService _authService = SupabaseAuthService();
  bool _isSignedIn = false; // Track user sign-in status

  // List of pages for signed-in users
  late final List<Widget> _pages;
  // Track the current page index (0: Coin, 1: Bo<PERSON>, 3: Wallet)
  int _currentPageIndex = 0;

  @override
  void initState() {
    super.initState();
    _checkAuthState();
    _setupAuthListener();
    _initializePages();
  }

  void _initializePages() {
    _pages = [
      ResponsiveCoinPage(
        onThemeToggle: widget.onThemeToggle,
        isDarkMode: widget.isDarkMode,
        isSignedIn: _isSignedIn,
        onSignIn: _handleSignIn,
      ),
      const BotPage(),
      const RecommendationPage(),
      const TradesPage(),
      const WalletPage(),
    ];
  }

  void _checkAuthState() {
    setState(() {
      _isSignedIn = _authService.isAuthenticated;
    });
  }

  void _handleSignIn() {
    setState(() {
      _isSignedIn = true;
    });
    _initializePages(); // Reinitialize pages with new auth state

    // Navigate to the Coins page (index 0) after successful sign-in
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _currentPageIndex = 0; // Navigate to Coins page
        });
      }
    });
  }

  void _setupAuthListener() {
    try {
      Supabase.instance.client.auth.onAuthStateChange.listen((data) {
        final AuthChangeEvent event = data.event;
        if (event == AuthChangeEvent.signedIn) {
          setState(() {
            _isSignedIn = true;
          });
          _initializePages(); // Reinitialize pages with new auth state
        } else if (event == AuthChangeEvent.signedOut) {
          setState(() {
            _isSignedIn = false;
          });
          _initializePages(); // Reinitialize pages with new auth state
        }
      });
    } catch (e) {
      debugPrint('Auth listener setup failed (offline mode): $e');
      // Continue without auth listener in offline mode
    }
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveHomeLayout(
      pages: _pages,
      currentPageIndex: _currentPageIndex,
      onPageChanged: (index) {
        setState(() {
          _currentPageIndex = index;
        });
      },
      isSignedIn: _isSignedIn,
      onSignIn: _handleSignIn,
    );
  }


}
