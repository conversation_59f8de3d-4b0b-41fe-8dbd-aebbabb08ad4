import 'package:dextrip_app/homepage.dart';
import 'package:flutter/material.dart';
import 'theme/app_theme.dart';
import 'services/supabase_auth_service.dart';
import 'services/deep_link_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services
  await SupabaseAuthService.initialize();
  await DeepLinkService.initialize();

  runApp(const DexTrip());
}

class DexTrip extends StatefulWidget {
  const DexTrip({super.key});

  @override
  State<DexTrip> createState() => _DexTripState();
}

class _DexTripState extends State<DexTrip> {
  ThemeMode _themeMode = ThemeMode.system;

  void _toggleTheme() {
    setState(() {
      _themeMode = _themeMode == ThemeMode.dark
          ? ThemeMode.light
          : ThemeMode.dark;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      themeMode: _themeMode,
      theme: AppTheme.darkTheme,
      // darkTheme: AppTheme.darkTheme,
      home: MyHomePage(
        title: 'Dextrip',
        onThemeToggle: _toggleTheme,
        isDarkMode: _themeMode == ThemeMode.dark,
      ),
    );
  }
}
