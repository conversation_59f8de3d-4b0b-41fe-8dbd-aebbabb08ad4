import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../theme/app_theme.dart';

class CoinChart extends StatefulWidget {
  final String symbol;
  final double currentPrice;

  const CoinChart({
    super.key,
    required this.symbol,
    required this.currentPrice,
  });

  @override
  State<CoinChart> createState() => _CoinChartState();
}

class _CoinChartState extends State<CoinChart> {
  String _selectedPeriod = '1H';
  bool _isLineChart = true;
  List<FlSpot> _chartData = [];
  Timer? _updateTimer;
  bool _isLoading = true;

  final List<String> _timePeriods = ['1H', '4H', '1D', '1W', '1M'];

  @override
  void initState() {
    super.initState();
    _generateInitialData();
    _startRealTimeUpdates();
  }

  @override
  void dispose() {
    _updateTimer?.cancel();
    super.dispose();
  }

  void _generateInitialData() {
    setState(() {
      _isLoading = true;
    });

    // Generate realistic price data based on current price
    final random = Random();
    final basePrice = widget.currentPrice;
    final dataPoints = _getDataPointsForPeriod(_selectedPeriod);

    _chartData.clear();

    double currentValue =
        basePrice *
        (0.95 + random.nextDouble() * 0.1); // Start within 5% of current price

    for (int i = 0; i < dataPoints; i++) {
      // Add some realistic price movement
      final change = (random.nextDouble() - 0.5) * 0.02; // ±1% change
      currentValue = currentValue * (1 + change);

      // Ensure we end close to the current price
      if (i == dataPoints - 1) {
        currentValue = basePrice;
      }

      _chartData.add(FlSpot(i.toDouble(), currentValue));
    }

    setState(() {
      _isLoading = false;
    });
  }

  int _getDataPointsForPeriod(String period) {
    switch (period) {
      case '1H':
        return 60; // 60 minutes
      case '4H':
        return 48; // 4 hours in 5-minute intervals
      case '1D':
        return 24; // 24 hours
      case '1W':
        return 7; // 7 days
      case '1M':
        return 30; // 30 days
      default:
        return 60;
    }
  }

  void _startRealTimeUpdates() {
    _updateTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (_chartData.isNotEmpty && mounted) {
        _updateChartData();
      }
    });
  }

  void _updateChartData() {
    if (_chartData.isEmpty) return;

    final random = Random();
    final lastPoint = _chartData.last;
    final change = (random.nextDouble() - 0.5) * 0.005; // ±0.25% change
    final newPrice = lastPoint.y * (1 + change);

    setState(() {
      // Remove first point and add new one for sliding window effect
      _chartData.removeAt(0);

      // Shift all x values down by 1
      for (int i = 0; i < _chartData.length; i++) {
        _chartData[i] = FlSpot(_chartData[i].x - 1, _chartData[i].y);
      }

      // Add new point at the end
      _chartData.add(FlSpot(_chartData.length.toDouble(), newPrice));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Chart
          Expanded(
            child: _isLoading
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppTheme.primaryColor,
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),
                        Text(
                          'Loading chart...',
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.6),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  )
                : _buildChart(),
          ),
          SizedBox(height: 16),
          Row(
            children: [
              // Chart type toggle
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildChartTypeButton(
                      icon: LucideIcons.trendingUp,
                      isSelected: _isLineChart,
                      onTap: () => setState(() => _isLineChart = true),
                    ),
                    _buildChartTypeButton(
                      icon: LucideIcons.activity,
                      isSelected: !_isLineChart,
                      onTap: () => setState(() => _isLineChart = false),
                    ),
                  ],
                ),
              ),

              const Spacer(),

              // Time period selector
              Row(
                children: _timePeriods.map((period) {
                  final isSelected = period == _selectedPeriod;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedPeriod = period;
                      });
                      _generateInitialData();
                    },
                    child: Container(
                      margin: const EdgeInsets.only(left: 8),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? AppTheme.primaryColor
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        period,
                        style: TextStyle(
                          color: isSelected
                              ? Colors.white
                              : Colors.white.withValues(alpha: 0.6),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChartTypeButton({
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Icon(
          icon,
          size: 16,
          color: isSelected
              ? Colors.white
              : Colors.white.withValues(alpha: 0.6),
        ),
      ),
    );
  }

  Widget _buildChart() {
    if (_chartData.isEmpty) {
      return Center(
        child: Text(
          'No data available',
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.6),
            fontSize: 14,
          ),
        ),
      );
    }

    final minY = _chartData.map((e) => e.y).reduce(min);
    final maxY = _chartData.map((e) => e.y).reduce(max);
    final padding = (maxY - minY) * 0.1;

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: (maxY - minY) / 4,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.white.withValues(alpha: 0.1),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: (maxY - minY) / 4,
              getTitlesWidget: (value, meta) {
                return Text(
                  '\$${value.toStringAsFixed(value < 1 ? 4 : 2)}',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.6),
                    fontSize: 10,
                  ),
                );
              },
              reservedSize: 60,
            ),
          ),
        ),
        borderData: FlBorderData(show: false),
        minX: 0,
        maxX: _chartData.length.toDouble() - 1,
        minY: minY - padding,
        maxY: maxY + padding,
        lineBarsData: [
          LineChartBarData(
            spots: _chartData,
            isCurved: true,
            gradient: LinearGradient(
              colors: [
                AppTheme.primaryColor,
                AppTheme.primaryColor.withValues(alpha: 0.8),
              ],
            ),
            barWidth: 2,
            isStrokeCapRound: true,
            dotData: const FlDotData(show: false),
            belowBarData: BarAreaData(
              show: true,
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  AppTheme.primaryColor.withValues(alpha: 0.3),
                  AppTheme.primaryColor.withValues(alpha: 0.0),
                ],
              ),
            ),
          ),
        ],
        lineTouchData: LineTouchData(
          enabled: true,
          touchTooltipData: LineTouchTooltipData(
            getTooltipColor: (touchedSpot) => Colors.black87,
            getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
              return touchedBarSpots.map((barSpot) {
                return LineTooltipItem(
                  '\$${barSpot.y.toStringAsFixed(barSpot.y < 1 ? 4 : 2)}',
                  const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                );
              }).toList();
            },
          ),
        ),
      ),
    );
  }
}
