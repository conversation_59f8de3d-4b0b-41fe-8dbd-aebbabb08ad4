import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../theme/app_theme.dart';
import '../widgets/gradient_button.dart';
import '../widgets/bot_config_modal.dart';
import '../models/trading_bot_model.dart';

enum BotConfigSection {
  main,
  tradingStrategy,
  indicators,
  riskManagement,
  tradingHours,
}

class CreateBotModal extends StatefulWidget {
  final String coinSymbol;
  final VoidCallback? onBotCreated;
  final bool isEditing;

  const CreateBotModal({
    super.key,
    required this.coinSymbol,
    this.onBotCreated,
    this.isEditing = false,
  });

  @override
  State<CreateBotModal> createState() => _CreateBotModalState();
}

class _CreateBotModalState extends State<CreateBotModal> {
  BotConfigSection _currentSection = BotConfigSection.main;

  // Bot configuration state
  // ignore: unused_field
  String _botName = '';
  String _selectedStrategy = 'DCA';
  bool _useStopLoss = true;
  bool _useTakeProfit = true;
  double _riskPercentage = 2.0;
  bool _tradingEnabled = true;

  @override
  void initState() {
    super.initState();
    _botName = '${widget.coinSymbol.toUpperCase()} Bot';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,

      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          _buildHeader(),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: _buildCurrentSectionContent(),
            ),
          ),

          // Fixed bottom button
          _buildBottomButton(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          if (_currentSection != BotConfigSection.main)
            IconButton(
              onPressed: () =>
                  setState(() => _currentSection = BotConfigSection.main),
              icon: const Icon(
                LucideIcons.arrowLeft,
                color: Colors.white,
                size: 20,
              ),
            ),

          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              LucideIcons.bot,
              color: AppTheme.primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getSectionTitle(),
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  _getSectionSubtitle(),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),

          // Add new bot button for indicators section
          if (_currentSection == BotConfigSection.indicators)
            IconButton(
              onPressed: _showBotConfigModal,
              icon: const Icon(LucideIcons.plus, color: Colors.white, size: 20),
              tooltip: 'Add New Bot',
            ),

          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(LucideIcons.x, color: Colors.white, size: 20),
          ),
        ],
      ),
    );
  }

  String _getSectionTitle() {
    switch (_currentSection) {
      case BotConfigSection.main:
        return widget.isEditing ? 'Edit Trading Bot' : 'Create Trading Bot';
      case BotConfigSection.tradingStrategy:
        return 'Trading Strategy';
      case BotConfigSection.indicators:
        return 'Technical Indicators';
      case BotConfigSection.riskManagement:
        return 'Risk Management';
      case BotConfigSection.tradingHours:
        return 'Trading Hours';
    }
  }

  String _getSectionSubtitle() {
    switch (_currentSection) {
      case BotConfigSection.main:
        return 'Configure bot for ${widget.coinSymbol.toUpperCase()}';
      case BotConfigSection.tradingStrategy:
        return 'Select your trading approach';
      case BotConfigSection.indicators:
        return 'Choose technical analysis tools';
      case BotConfigSection.riskManagement:
        return 'Set risk parameters';
      case BotConfigSection.tradingHours:
        return 'Define active trading periods';
    }
  }

  Widget _buildCurrentSectionContent() {
    switch (_currentSection) {
      case BotConfigSection.main:
        return _buildMainContent();
      case BotConfigSection.tradingStrategy:
        return _buildTradingStrategyContent();
      case BotConfigSection.indicators:
        return _buildIndicatorsContent();
      case BotConfigSection.riskManagement:
        return _buildRiskManagementContent();
      case BotConfigSection.tradingHours:
        return _buildTradingHoursContent();
    }
  }

  Widget _buildMainContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Bot Name
        _buildInputSection(
          'Bot Name',
          'Give your bot a memorable name',
          TextField(
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'Enter bot name',
              hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.5)),
              filled: true,
              fillColor: Colors.white.withValues(alpha: 0.05),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.white.withValues(alpha: 0.1),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.white.withValues(alpha: 0.1),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.primaryColor),
              ),
            ),
            onChanged: (value) => setState(() => _botName = value),
          ),
        ),

        const SizedBox(height: 32),

        // Configuration Sections
        _buildConfigSection(
          'Trading Strategy',
          'DCA, Grid Trading, Momentum',
          LucideIcons.trendingUp,
          () => setState(
            () => _currentSection = BotConfigSection.tradingStrategy,
          ),
        ),

        _buildConfigSection(
          'Technical Indicators',
          'RSI, MACD, Bollinger Bands',
          LucideIcons.activity,
          () => setState(() => _currentSection = BotConfigSection.indicators),
        ),

        _buildConfigSection(
          'Risk Management',
          'Stop Loss, Take Profit, Position Size',
          LucideIcons.shield,
          () =>
              setState(() => _currentSection = BotConfigSection.riskManagement),
        ),

        _buildConfigSection(
          'Trading Hours',
          '24/7 or Custom Schedule',
          LucideIcons.clock,
          () => setState(() => _currentSection = BotConfigSection.tradingHours),
        ),

        const SizedBox(height: 32),
      ],
    );
  }

  Widget _buildInputSection(String title, String subtitle, Widget child) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          subtitle,
          style: TextStyle(
            fontSize: 14,
            color: Colors.white.withValues(alpha: 0.6),
          ),
        ),
        const SizedBox(height: 16),
        child,
      ],
    );
  }

  Widget _buildConfigSection(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: AppTheme.primaryColor, size: 20),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  LucideIcons.chevronRight,
                  color: Colors.white.withValues(alpha: 0.4),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomButton() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: const Color(0xFF0F0F0F),
        border: Border(
          top: BorderSide(color: Colors.white.withValues(alpha: 0.1), width: 1),
        ),
      ),
      child: SafeArea(
        child: GradientButton(
          text: _currentSection == BotConfigSection.main
              ? (widget.isEditing ? 'Update Bot' : 'Start Trading Bot')
              : 'Save Configuration',
          onPressed: _handleButtonPress,
          icon: _currentSection == BotConfigSection.main
              ? LucideIcons.play
              : LucideIcons.check,
        ),
      ),
    );
  }

  void _handleButtonPress() {
    if (_currentSection == BotConfigSection.main) {
      // Start/Update bot
      Navigator.pop(context);
      widget.onBotCreated?.call();
      _showBotCreatedSnackbar();
    } else {
      // Save section configuration and go back to main
      setState(() => _currentSection = BotConfigSection.main);
    }
  }

  void _showBotCreatedSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(LucideIcons.check, color: Colors.green, size: 20),
            const SizedBox(width: 12),
            Text(
              widget.isEditing
                  ? 'Bot updated successfully'
                  : 'Trading bot started for ${widget.coinSymbol.toUpperCase()}',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green.withValues(alpha: 0.9),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Widget _buildTradingStrategyContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildStrategyOption(
          'DCA (Dollar Cost Averaging)',
          'Invest fixed amounts at regular intervals',
          _selectedStrategy == 'DCA',
          () => setState(() => _selectedStrategy = 'DCA'),
        ),
        _buildStrategyOption(
          'Grid Trading',
          'Place buy and sell orders at regular intervals',
          _selectedStrategy == 'Grid',
          () => setState(() => _selectedStrategy = 'Grid'),
        ),
        _buildStrategyOption(
          'Momentum Trading',
          'Follow price trends and momentum',
          _selectedStrategy == 'Momentum',
          () => setState(() => _selectedStrategy = 'Momentum'),
        ),
        _buildStrategyOption(
          'Mean Reversion',
          'Buy low, sell high based on averages',
          _selectedStrategy == 'MeanReversion',
          () => setState(() => _selectedStrategy = 'MeanReversion'),
        ),
        const SizedBox(height: 32),
      ],
    );
  }

  Widget _buildIndicatorsContent() {
    // Create a dummy bot for configuration
    final dummyBot = TradingBot(
      id: 'indicator_config_bot',
      name: '${widget.coinSymbol} Indicator Bot',
      tokenSymbol: widget.coinSymbol,
      tokenImage: '',
      currentPrice: 0.0,
      priceChange24h: 0.0,
      isActive: false,
      strategy: 'DCA',
      solPerTrade: 0.1,
      totalTrades: 0,
      winRate: 0.0,
      totalEarned: 0.0,
      trades: [],
    );

    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.6,
      child: BotConfigModal(bot: dummyBot, isCreating: true),
    );
  }

  void _showBotConfigModal() {
    // Create a dummy bot for configuration
    final dummyBot = TradingBot(
      id: 'new_bot',
      name: '${widget.coinSymbol} New Bot',
      tokenSymbol: widget.coinSymbol,
      tokenImage: '',
      currentPrice: 0.0,
      priceChange24h: 0.0,
      isActive: false,
      strategy: 'DCA',
      solPerTrade: 0.1,
      totalTrades: 0,
      winRate: 0.0,
      totalEarned: 0.0,
      trades: [],
    );

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => BotConfigModal(bot: dummyBot, isCreating: true),
    );
  }

  Widget _buildRiskManagementContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildRiskToggle(
          'Stop Loss',
          'Automatically sell to limit losses',
          _useStopLoss,
          (value) => setState(() => _useStopLoss = value),
        ),
        _buildRiskToggle(
          'Take Profit',
          'Automatically sell to secure profits',
          _useTakeProfit,
          (value) => setState(() => _useTakeProfit = value),
        ),
        const SizedBox(height: 24),
        _buildRiskSlider(
          'Risk Per Trade',
          'Percentage of portfolio to risk per trade',
          _riskPercentage,
          1.0,
          10.0,
          (value) => setState(() => _riskPercentage = value),
        ),
        const SizedBox(height: 32),
      ],
    );
  }

  Widget _buildTradingHoursContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTradingToggle(
          '24/7 Trading',
          'Trade continuously without breaks',
          _tradingEnabled,
          (value) => setState(() => _tradingEnabled = value),
        ),
        const SizedBox(height: 24),
        if (!_tradingEnabled) ...[
          Text(
            'Custom Trading Hours',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          _buildTimeSelector('Start Time', '09:00'),
          const SizedBox(height: 12),
          _buildTimeSelector('End Time', '17:00'),
        ],
        const SizedBox(height: 32),
      ],
    );
  }

  Widget _buildStrategyOption(
    String title,
    String description,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppTheme.primaryColor.withValues(alpha: 0.1)
                  : Colors.white.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected
                    ? AppTheme.primaryColor
                    : Colors.white.withValues(alpha: 0.1),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected
                          ? AppTheme.primaryColor
                          : Colors.white.withValues(alpha: 0.4),
                      width: 2,
                    ),
                  ),
                  child: isSelected
                      ? Center(
                          child: Container(
                            width: 10,
                            height: 10,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIndicatorToggle(
    String title,
    String description,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.primaryColor,
            activeTrackColor: AppTheme.primaryColor.withValues(alpha: 0.3),
            inactiveThumbColor: Colors.white.withValues(alpha: 0.6),
            inactiveTrackColor: Colors.white.withValues(alpha: 0.1),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRiskToggle(
    String title,
    String description,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return _buildIndicatorToggle(title, description, value, onChanged);
  }

  Widget _buildTradingToggle(
    String title,
    String description,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return _buildIndicatorToggle(title, description, value, onChanged);
  }

  Widget _buildRiskSlider(
    String title,
    String description,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
              Text(
                '${value.toStringAsFixed(1)}%',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: AppTheme.primaryColor,
              inactiveTrackColor: Colors.white.withValues(alpha: 0.2),
              thumbColor: AppTheme.primaryColor,
              overlayColor: AppTheme.primaryColor.withValues(alpha: 0.2),
              trackHeight: 4,
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              divisions: ((max - min) * 2).toInt(),
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSelector(String label, String time) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          Row(
            children: [
              Text(
                time,
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                LucideIcons.clock,
                color: Colors.white.withValues(alpha: 0.4),
                size: 16,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
