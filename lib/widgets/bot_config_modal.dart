import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../theme/app_theme.dart';
import '../models/trading_bot_model.dart';

enum BotConfigSection {
  main,
  strategySelection,
  solPerTradeSettings,
  maxTradesSettings,
  riskSettings,
  timezoneSettings,
  indicatorSettings,
  addNewIndicator,
}

class BotConfigModal extends StatefulWidget {
  final TradingBot bot;
  final bool isCreating;

  const BotConfigModal({super.key, required this.bot, this.isCreating = false});

  @override
  State<BotConfigModal> createState() => _BotConfigModalState();
}

class CreateBotModal extends StatefulWidget {
  final String coinSymbol;
  final VoidCallback onBotCreated;

  const CreateBotModal({
    super.key,
    required this.coinSymbol,
    required this.onBotCreated,
  });

  @override
  State<CreateBotModal> createState() => _CreateBotModalState();
}

class _BotConfigModalState extends State<BotConfigModal> {
  BotConfigSection _currentSection = BotConfigSection.main;
  String _selectedStrategy = 'DCA';
  String _selectedIndicator = '';

  // Indicator settings
  bool _useRSI = true;
  bool _useMACD = true;
  bool _useBollingerBands = false;
  bool _useEMA = true;
  bool _useVolumeProfile = false;

  // RSI Settings
  int _rsiPeriod = 14;
  int _rsiOverbought = 70;
  int _rsiOversold = 30;

  // Risk settings
  double _stopLossPercentage = 5.0;
  double _takeProfitPercentage = 15.0;

  // Trading settings
  double _solPerTrade = 0.1;
  int _maxDailyTrades = 50;

  // Timezone
  String _selectedTimezone = 'UTC';

  @override
  void initState() {
    super.initState();
    _selectedStrategy = widget.bot.strategy;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white24,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          _buildHeader(),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: _buildCurrentSectionContent(),
            ),
          ),

          // Fixed bottom button
          _buildBottomButton(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          if (_currentSection != BotConfigSection.main)
            IconButton(
              onPressed: () =>
                  setState(() => _currentSection = BotConfigSection.main),
              icon: const Icon(
                LucideIcons.arrowLeft,
                color: Colors.white,
                size: 20,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),

          if (_currentSection != BotConfigSection.main)
            const SizedBox(width: 12),

          Expanded(
            child: Text(
              _getSectionTitle(),
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),

          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(LucideIcons.x, color: Colors.white, size: 20),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  String _getSectionTitle() {
    switch (_currentSection) {
      case BotConfigSection.main:
        return 'CONFIGURE BOT';
      case BotConfigSection.strategySelection:
        return 'Select Strategy';
      case BotConfigSection.solPerTradeSettings:
        return 'SOL per Trade';
      case BotConfigSection.maxTradesSettings:
        return 'Max Daily Trades';
      case BotConfigSection.riskSettings:
        return 'Risk Settings';
      case BotConfigSection.timezoneSettings:
        return 'Timezone Settings';
      case BotConfigSection.indicatorSettings:
        return '$_selectedIndicator Settings';
      case BotConfigSection.addNewIndicator:
        return 'Add New Indicator';
    }
  }

  Widget _buildCurrentSectionContent() {
    switch (_currentSection) {
      case BotConfigSection.main:
        return _buildMainContent();
      case BotConfigSection.strategySelection:
        return _buildStrategySelection();
      case BotConfigSection.solPerTradeSettings:
        return _buildSolPerTradeSettings();
      case BotConfigSection.maxTradesSettings:
        return _buildMaxTradesSettings();
      case BotConfigSection.riskSettings:
        return _buildRiskSettings();
      case BotConfigSection.timezoneSettings:
        return _buildTimezoneSettings();
      case BotConfigSection.indicatorSettings:
        return _buildIndicatorSettings();
      case BotConfigSection.addNewIndicator:
        return _buildAddNewIndicatorList();
    }
  }

  Widget _buildBottomButton() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(top: BorderSide(color: Colors.white24, width: 1)),
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          child: Container(
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(12),
            ),
            child: ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                if (widget.isCreating) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Trading bot created successfully!'),
                      backgroundColor: Colors.green,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Bot configuration saved!'),
                      backgroundColor: Colors.green,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                shadowColor: Colors.transparent,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                _currentSection == BotConfigSection.main
                    ? (widget.isCreating ? 'START BOT' : 'Save Configuration')
                    : _currentSection == BotConfigSection.addNewIndicator
                    ? 'Create Indicator'
                    : 'Save Settings',
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Trading Strategy Section
        _buildConfigSection('Trading Strategy', [
          _buildClickableConfigItem(
            'Strategy Type',
            _selectedStrategy,
            LucideIcons.trendingUp,
            () => setState(
              () => _currentSection = BotConfigSection.strategySelection,
            ),
          ),
          _buildClickableConfigItem(
            'SOL per Trade',
            '${_solPerTrade.toStringAsFixed(3)} SOL',
            LucideIcons.coins,
            () => setState(
              () => _currentSection = BotConfigSection.solPerTradeSettings,
            ),
          ),
          _buildClickableConfigItem(
            'Max Daily Trades',
            '$_maxDailyTrades',
            LucideIcons.clock,
            () => setState(
              () => _currentSection = BotConfigSection.maxTradesSettings,
            ),
          ),
        ]),

        const SizedBox(height: 24),

        // Risk Management Section
        _buildConfigSection('Risk Management', [
          _buildClickableConfigItem(
            'Stop Loss',
            '${_stopLossPercentage.toStringAsFixed(1)}%',
            LucideIcons.shield,
            () =>
                setState(() => _currentSection = BotConfigSection.riskSettings),
          ),
          _buildClickableConfigItem(
            'Take Profit',
            '${_takeProfitPercentage.toStringAsFixed(1)}%',
            LucideIcons.target,
            () =>
                setState(() => _currentSection = BotConfigSection.riskSettings),
          ),
          _buildConfigItem('Max Drawdown', '20%', LucideIcons.trendingDown),
        ]),

        const SizedBox(height: 24),

        // Trading Hours Section
        _buildConfigSection('Trading Hours', [
          _buildConfigToggle(
            '24/7 Trading',
            widget.bot.isActive,
            'Trade around the clock',
          ),
          _buildClickableConfigItem(
            'Timezone',
            _selectedTimezone,
            LucideIcons.globe,
            () => setState(
              () => _currentSection = BotConfigSection.timezoneSettings,
            ),
          ),
          _buildConfigItem(
            'Market Hours Only',
            'Disabled',
            LucideIcons.calendar,
          ),
        ]),

        const SizedBox(height: 24),

        // Technical Indicators Section
        _buildConfigSection('Technical Indicators', [
          _buildIndicatorToggle(
            'RSI (14)',
            _useRSI,
            'Relative Strength Index',
            () => setState(() {
              _selectedIndicator = 'RSI (14)';
              _currentSection = BotConfigSection.indicatorSettings;
            }),
          ),
          _buildIndicatorToggle(
            'MACD',
            _useMACD,
            'Moving Average Convergence Divergence',
            () => setState(() {
              _selectedIndicator = 'MACD';
              _currentSection = BotConfigSection.indicatorSettings;
            }),
          ),
          _buildIndicatorToggle(
            'Bollinger Bands',
            _useBollingerBands,
            'Price volatility indicator',
            () => setState(() {
              _selectedIndicator = 'Bollinger Bands';
              _currentSection = BotConfigSection.indicatorSettings;
            }),
          ),
          _buildIndicatorToggle(
            'EMA (20)',
            _useEMA,
            'Exponential Moving Average',
            () => setState(() {
              _selectedIndicator = 'EMA (20)';
              _currentSection = BotConfigSection.indicatorSettings;
            }),
          ),
          _buildIndicatorToggle(
            'Volume Profile',
            _useVolumeProfile,
            'Trading volume analysis',
            () => setState(() {
              _selectedIndicator = 'Volume Profile';
              _currentSection = BotConfigSection.indicatorSettings;
            }),
          ),

          // Add new indicator option
          _buildAddNewIndicatorOption(),
        ]),

        const SizedBox(height: 100), // Space for fixed button
      ],
    );
  }

  Widget _buildStrategySelection() {
    final strategies = [
      {
        'name': 'DCA',
        'description': 'Dollar Cost Averaging - Regular interval investments',
      },
      {
        'name': 'Grid Trading',
        'description': 'Place buy and sell orders at regular intervals',
      },
      {'name': 'Momentum', 'description': 'Follow price trends and momentum'},
      {
        'name': 'Mean Reversion',
        'description': 'Buy low, sell high based on averages',
      },
      {'name': 'Scalping', 'description': 'High-frequency short-term trades'},
    ];

    return Column(
      children: strategies.map((strategy) {
        final isSelected = _selectedStrategy == strategy['name'];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                setState(() {
                  _selectedStrategy = strategy['name']!;
                  _currentSection = BotConfigSection.main;
                });
              },
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isSelected
                      ? Colors.white.withValues(alpha: 0.2)
                      : Colors.white.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.1),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isSelected
                              ? Colors.white
                              : Colors.white.withValues(alpha: 0.4),
                          width: 2,
                        ),
                      ),
                      child: isSelected
                          ? Center(
                              child: Container(
                                width: 10,
                                height: 10,
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.white,
                                ),
                              ),
                            )
                          : null,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            strategy['name']!,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            strategy['description']!,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.white.withValues(alpha: 0.6),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSolPerTradeSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'SOL per Trade Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting(
          'SOL Amount',
          _solPerTrade,
          0.01,
          10.0,
          (value) => setState(() => _solPerTrade = value),
        ),

        const SizedBox(height: 20),

        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Estimated Trade Value',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '\$${(_solPerTrade * 150).toStringAsFixed(2)} USD',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Based on current SOL price (~\$150)',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMaxTradesSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Max Daily Trades Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting(
          'Max Trades per Day',
          _maxDailyTrades.toDouble(),
          1,
          200,
          (value) => setState(() => _maxDailyTrades = value.toInt()),
        ),

        const SizedBox(height: 20),

        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Trading Frequency',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${(1440 / _maxDailyTrades).toStringAsFixed(1)} minutes',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Average time between trades',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              Icon(LucideIcons.info, color: Colors.white, size: 20),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Trading Tip',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Higher trade limits allow for more opportunities but may increase risk. Consider market volatility when setting limits.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildIndicatorSettings() {
    switch (_selectedIndicator) {
      case 'RSI (14)':
        return _buildRSISettings();
      case 'MACD':
        return _buildMACDSettings();
      case 'Bollinger Bands':
        return _buildBollingerSettings();
      case 'EMA (20)':
        return _buildEMASettings();
      case 'Volume Profile':
        return _buildVolumeSettings();
      case 'Stochastic RSI':
        return _buildStochasticRSISettings();
      case 'Williams %R':
        return _buildWilliamsRSettings();
      case 'Commodity Channel Index (CCI)':
        return _buildCCISettings();
      case 'Average True Range (ATR)':
        return _buildATRSettings();
      case 'Parabolic SAR':
        return _buildParabolicSARSettings();
      case 'Ichimoku Cloud':
        return _buildIchimokuSettings();
      default:
        return _buildDefaultIndicatorSettings();
    }
  }

  Widget _buildRSISettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'RSI Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting(
          'Period',
          _rsiPeriod.toDouble(),
          5,
          50,
          (value) => setState(() => _rsiPeriod = value.toInt()),
        ),

        const SizedBox(height: 20),

        _buildSliderSetting(
          'Overbought Level',
          _rsiOverbought.toDouble(),
          60,
          90,
          (value) => setState(() => _rsiOverbought = value.toInt()),
        ),

        const SizedBox(height: 20),

        _buildSliderSetting(
          'Oversold Level',
          _rsiOversold.toDouble(),
          10,
          40,
          (value) => setState(() => _rsiOversold = value.toInt()),
        ),
      ],
    );
  }

  Widget _buildMACDSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'MACD Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Fast Period', 12, 5, 20, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Slow Period', 26, 20, 50, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Signal Period', 9, 5, 15, (value) {}),
      ],
    );
  }

  Widget _buildBollingerSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Bollinger Bands Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Period', 20, 10, 50, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Standard Deviation', 2, 1, 3, (value) {}),
      ],
    );
  }

  Widget _buildEMASettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'EMA Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Period', 20, 5, 200, (value) {}),
      ],
    );
  }

  Widget _buildVolumeSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Volume Profile Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Lookback Period', 100, 50, 500, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Volume Threshold', 150, 100, 300, (value) {}),
      ],
    );
  }

  Widget _buildStochasticRSISettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Stochastic RSI Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('K Period', 14, 5, 50, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('D Period', 3, 1, 10, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('RSI Period', 14, 5, 50, (value) {}),
      ],
    );
  }

  Widget _buildWilliamsRSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Williams %R Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Period', 14, 5, 50, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Overbought Level', -20, -30, -10, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Oversold Level', -80, -90, -70, (value) {}),
      ],
    );
  }

  Widget _buildCCISettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'CCI Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Period', 20, 5, 50, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Overbought Level', 100, 50, 200, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Oversold Level', -100, -200, -50, (value) {}),
      ],
    );
  }

  Widget _buildATRSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'ATR Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Period', 14, 5, 50, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Multiplier', 2, 1, 5, (value) {}),
      ],
    );
  }

  Widget _buildParabolicSARSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Parabolic SAR Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Start AF', 0.02, 0.01, 0.1, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Max AF', 0.2, 0.1, 0.5, (value) {}),
      ],
    );
  }

  Widget _buildIchimokuSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Ichimoku Cloud Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting('Tenkan Period', 9, 5, 20, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Kijun Period', 26, 15, 50, (value) {}),
        const SizedBox(height: 20),
        _buildSliderSetting('Senkou B Period', 52, 30, 100, (value) {}),
      ],
    );
  }

  Widget _buildDefaultIndicatorSettings() {
    return const Column(
      children: [
        Text(
          'No specific settings available for this indicator.',
          style: TextStyle(fontSize: 16, color: Colors.white),
        ),
      ],
    );
  }

  Widget _buildRiskSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Risk Management',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        _buildSliderSetting(
          'Stop Loss (%)',
          _stopLossPercentage,
          1,
          20,
          (value) => setState(() => _stopLossPercentage = value),
        ),

        const SizedBox(height: 20),

        _buildSliderSetting(
          'Take Profit (%)',
          _takeProfitPercentage,
          5,
          50,
          (value) => setState(() => _takeProfitPercentage = value),
        ),
      ],
    );
  }

  Widget _buildTimezoneSettings() {
    final timezones = ['UTC', 'EST', 'PST', 'GMT', 'CET', 'JST', 'AEST'];

    return Column(
      children: timezones.map((timezone) {
        final isSelected = _selectedTimezone == timezone;
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                setState(() {
                  _selectedTimezone = timezone;
                  _currentSection = BotConfigSection.main;
                });
              },
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isSelected
                      ? Colors.white.withValues(alpha: 0.2)
                      : Colors.white.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.1),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isSelected
                              ? Colors.white
                              : Colors.white.withValues(alpha: 0.4),
                          width: 2,
                        ),
                      ),
                      child: isSelected
                          ? Center(
                              child: Container(
                                width: 10,
                                height: 10,
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.white,
                                ),
                              ),
                            )
                          : null,
                    ),
                    const SizedBox(width: 16),
                    Text(
                      timezone,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildConfigSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildConfigItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: Colors.white, size: 16),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClickableConfigItem(
    String label,
    String value,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: Colors.white, size: 16),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    label,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  LucideIcons.chevronRight,
                  color: Colors.white.withValues(alpha: 0.4),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIndicatorToggle(
    String label,
    bool value,
    String description,
    VoidCallback onSettingsTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Switch(
            value: value,
            onChanged: (newValue) {
              setState(() {
                switch (label) {
                  case 'RSI (14)':
                    _useRSI = newValue;
                    break;
                  case 'MACD':
                    _useMACD = newValue;
                    break;
                  case 'Bollinger Bands':
                    _useBollingerBands = newValue;
                    break;
                  case 'EMA (20)':
                    _useEMA = newValue;
                    break;
                  case 'Volume Profile':
                    _useVolumeProfile = newValue;
                    break;
                }
              });
            },
            activeColor: Colors.white,
            activeTrackColor: Colors.white.withValues(alpha: 0.3),
            inactiveThumbColor: Colors.white.withValues(alpha: 0.6),
            inactiveTrackColor: Colors.white.withValues(alpha: 0.1),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
          if (value)
            GestureDetector(
              onTap: onSettingsTap,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  LucideIcons.settings,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildConfigToggle(String label, bool value, String description) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Switch(
            value: value,
            onChanged: (newValue) {
              // Handle toggle changes
            },
            activeColor: Colors.white,
            activeTrackColor: Colors.white.withValues(alpha: 0.3),
            inactiveThumbColor: Colors.white.withValues(alpha: 0.6),
            inactiveTrackColor: Colors.white.withValues(alpha: 0.1),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddNewIndicatorOption() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // Show add new indicator dialog
            _showAddIndicatorDialog();
          },
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                style: BorderStyle.solid,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    LucideIcons.plus,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Add New Indicator',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
                Icon(
                  LucideIcons.chevronRight,
                  color: Colors.white.withValues(alpha: 0.6),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSliderSetting(
    String label,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              Text(
                value < 1 ? value.toStringAsFixed(3) : value.toInt().toString(),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Colors.white,
              inactiveTrackColor: Colors.white.withValues(alpha: 0.2),
              thumbColor: Colors.white,
              overlayColor: Colors.white.withValues(alpha: 0.2),
              trackHeight: 4,
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              divisions: (max - min).toInt(),
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  void _showAddIndicatorDialog() {
    setState(() => _currentSection = BotConfigSection.addNewIndicator);
  }

  Widget _buildAddNewIndicatorList() {
    final availableIndicators = [
      {
        'name': 'Stochastic RSI',
        'description':
            'Combines Stochastic oscillator with RSI for momentum analysis',
        'enabled': false,
      },
      {
        'name': 'Williams %R',
        'description':
            'Momentum indicator measuring overbought/oversold levels',
        'enabled': false,
      },
      {
        'name': 'Commodity Channel Index (CCI)',
        'description': 'Identifies cyclical trends in commodity prices',
        'enabled': false,
      },
      {
        'name': 'Average True Range (ATR)',
        'description': 'Measures market volatility',
        'enabled': false,
      },
      {
        'name': 'Parabolic SAR',
        'description': 'Determines potential reversal points in price',
        'enabled': false,
      },
      {
        'name': 'Ichimoku Cloud',
        'description':
            'Comprehensive indicator showing support, resistance, and momentum',
        'enabled': false,
      },
    ];

    return Column(
      children: [
        ...availableIndicators.map((indicator) {
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  setState(() {
                    _selectedIndicator = indicator['name'] as String;
                    _currentSection = BotConfigSection.indicatorSettings;
                  });
                },
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.1),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          LucideIcons.trendingUp,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              indicator['name'] as String,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              indicator['description'] as String,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.white.withValues(alpha: 0.6),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Icon(
                        LucideIcons.chevronRight,
                        color: Colors.white.withValues(alpha: 0.4),
                        size: 16,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }).toList(),

        const SizedBox(height: 100), // Space for fixed button
      ],
    );
  }
}

class _CreateBotModalState extends State<CreateBotModal> {
  late TradingBot _dummyBot;

  @override
  void initState() {
    super.initState();
    // Create a dummy bot for configuration
    _dummyBot = TradingBot(
      id: 'temp',
      name: '${widget.coinSymbol} Trader',
      tokenSymbol: widget.coinSymbol,
      tokenImage: '',
      currentPrice: 0.0,
      priceChange24h: 0.0,
      isActive: false,
      strategy: 'DCA',
      solPerTrade: 0.1,
      totalTrades: 0,
      winRate: 0.0,
      totalEarned: 0.0,
      trades: [],
    );
  }

  @override
  Widget build(BuildContext context) {
    return BotConfigModal(bot: _dummyBot, isCreating: true);
  }
}
