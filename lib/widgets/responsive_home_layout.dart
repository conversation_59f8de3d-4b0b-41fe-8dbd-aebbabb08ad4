import 'package:flutter/material.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../responsive.dart';
import '../theme/app_theme.dart';
import '../widgets/desktop_sidebar.dart';
import '../widgets/gradient_button.dart';

class ResponsiveHomeLayout extends StatelessWidget {
  final List<Widget> pages;
  final int currentPageIndex;
  final Function(int) onPageChanged;
  final bool isSignedIn;
  final VoidCallback onSignIn;

  const ResponsiveHomeLayout({
    super.key,
    required this.pages,
    required this.currentPageIndex,
    required this.onPageChanged,
    required this.isSignedIn,
    required this.onSignIn,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (ResponsiveHelper.isDesktop(context)) {
          return _buildDesktopLayout(context);
        } else {
          return _buildMobileLayout(context);
        }
      },
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // Desktop Sidebar
          DesktopSidebar(
            currentPageIndex: currentPageIndex,
            onPageChanged: onPageChanged,
            isSignedIn: isSignedIn,
            onSignIn: onSignIn,
          ),
          // Main content
          Expanded(
            child: pages[currentPageIndex],
          ),
        ],
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Scaffold(
      body: pages[currentPageIndex],
      bottomNavigationBar: !isSignedIn
          ? _buildGetStartedFooter(context)
          : SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 8,
                ),
                child: GNav(
                  gap: 8,
                  activeColor: Theme.of(context).colorScheme.primary,
                  iconSize: 24,
                  duration: const Duration(milliseconds: 250),
                  tabBackgroundColor: Theme.of(context)
                      .colorScheme
                      .primary
                      .withValues(alpha: 0.1),
                  color: Theme.of(context)
                          .textTheme
                          .bodySmall
                          ?.color
                          ?.withValues(alpha: 0.6) ??
                      Colors.grey[600]!,
                  selectedIndex: currentPageIndex,
                  onTabChange: onPageChanged,
                  tabs: [
                    GButton(
                      icon: LucideIcons.circleDot,
                      iconSize: 22,
                      padding: const EdgeInsets.all(12),
                      iconActiveColor: Colors.white,
                      iconColor: currentPageIndex == 0
                          ? Colors.white
                          : Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.color
                                  ?.withValues(alpha: 0.6) ??
                              Colors.grey[600]!,
                      backgroundColor: currentPageIndex == 0
                          ? AppTheme.primaryColor
                          : Colors.transparent,
                      backgroundGradient: currentPageIndex == 0
                          ? AppTheme.primaryGradient
                          : null,
                    ),
                    if (isSignedIn) ...[
                      GButton(
                        icon: LucideIcons.bot,
                        iconSize: 22,
                        padding: const EdgeInsets.all(12),
                        iconActiveColor: Colors.white,
                        iconColor: currentPageIndex == 1
                            ? Colors.white
                            : Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.color
                                    ?.withValues(alpha: 0.6) ??
                                Colors.grey[600]!,
                        backgroundColor: currentPageIndex == 1
                            ? AppTheme.primaryColor
                            : Colors.transparent,
                        backgroundGradient: currentPageIndex == 1
                            ? AppTheme.primaryGradient
                            : null,
                      ),
                      GButton(
                        icon: LucideIcons.lightbulb,
                        iconSize: 22,
                        padding: const EdgeInsets.all(12),
                        iconActiveColor: Colors.white,
                        iconColor: currentPageIndex == 2
                            ? Colors.white
                            : Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.color
                                    ?.withValues(alpha: 0.6) ??
                                Colors.grey[600]!,
                        backgroundColor: currentPageIndex == 2
                            ? AppTheme.primaryColor
                            : Colors.transparent,
                        backgroundGradient: currentPageIndex == 2
                            ? AppTheme.primaryGradient
                            : null,
                      ),
                      GButton(
                        icon: LucideIcons.trendingUp,
                        iconSize: 22,
                        padding: const EdgeInsets.all(12),
                        iconActiveColor: Colors.white,
                        iconColor: currentPageIndex == 3
                            ? Colors.white
                            : Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.color
                                    ?.withValues(alpha: 0.6) ??
                                Colors.grey[600]!,
                        backgroundColor: currentPageIndex == 3
                            ? AppTheme.primaryColor
                            : Colors.transparent,
                        backgroundGradient: currentPageIndex == 3
                            ? AppTheme.primaryGradient
                            : null,
                      ),
                      GButton(
                        icon: LucideIcons.wallet,
                        iconSize: 22,
                        padding: const EdgeInsets.all(12),
                        iconActiveColor: Colors.white,
                        iconColor: currentPageIndex == 4
                            ? Colors.white
                            : Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.color
                                    ?.withValues(alpha: 0.6) ??
                                Colors.grey[600]!,
                        backgroundColor: currentPageIndex == 4
                            ? AppTheme.primaryColor
                            : Colors.transparent,
                        backgroundGradient: currentPageIndex == 4
                            ? AppTheme.primaryGradient
                            : null,
                      ),
                    ],
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildGetStartedFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Sign in to access all features',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
            const SizedBox(height: 12),
            GradientButton(
              text: 'Get Started',
              onPressed: onSignIn,
              icon: LucideIcons.arrowRight,
            ),
          ],
        ),
      ),
    );
  }
}
