import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'seed_phrase_modal.dart';
import 'import_wallet_modal.dart';

class AuthModal extends StatefulWidget {
  final VoidCallback? onSuccess;
  final bool isSignIn;

  const AuthModal({super.key, this.onSuccess, this.isSignIn = false});

  @override
  State<AuthModal> createState() => _AuthModalState();
}

class _AuthModalState extends State<AuthModal> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 24,
        right: 24,
        top: 24,
        bottom: MediaQuery.of(context).viewInsets.bottom + 24,
      ),
      decoration: const BoxDecoration(
        color: Color(0xFF1A1A1A),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(bottom: 20),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white24,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Row(
            children: [
              // Container(
              //   width: 50,
              //   height: 50,
              //   decoration: BoxDecoration(
              //     gradient: AppTheme.primaryGradient,
              //     borderRadius: BorderRadius.circular(25),
              //   ),
              //   child: const Icon(
              //     LucideIcons.wallet,
              //     color: Colors.white,
              //     size: 24,
              //   ),
              // ),
              // const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.isSignIn ? 'Sign In' : 'Get Started',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      widget.isSignIn
                          ? 'Access your existing wallet'
                          : 'Create your DexTrip wallet',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(LucideIcons.x, color: Colors.white54),
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Authentication options
          if (!widget.isSignIn) ...[
            // Create new wallet options
            // _buildAuthOption(
            //   icon: Icons.email_outlined,
            //   title: 'Continue with Email',
            //   subtitle: 'Sign up with email verification',
            //   onTap: () {
            //     Navigator.pop(context);
            //     _showEmailAuth();
            //   },
            // ),
            // const SizedBox(height: 12),
            // _buildAuthOption(
            //   icon: Icons.apple,
            //   title: 'Continue with Apple',
            //   subtitle: 'Quick sign up with Apple ID',
            //   onTap: () => _signInWithApple(),
            // ),
            const SizedBox(height: 12),
            _buildAuthOption(
              icon: Icons.g_mobiledata,
              title: 'Continue with Google',
              subtitle: 'Quick sign up with Google account',
              onTap: () => _signInWithGoogle(),
            ),
            const SizedBox(height: 12),
            _buildAuthOption(
              icon: LucideIcons.key,
              title: 'Create with Seed Phrase',
              subtitle: 'Advanced setup with recovery phrase',
              onTap: () {
                Navigator.pop(context);
                _showSeedPhraseCreation();
              },
            ),
          ] else ...[
            // Sign in options
            // _buildAuthOption(
            //   icon: Icons.email_outlined,
            //   title: 'Sign in with Email',
            //   subtitle: 'Use your email and verification code',
            //   onTap: () {
            //     Navigator.pop(context);
            //     _showEmailAuth();
            //   },
            // ),
            // const SizedBox(height: 12),
            // _buildAuthOption(
            //   icon: Icons.apple,
            //   title: 'Sign in with Apple',
            //   subtitle: 'Use your Apple ID',
            //   onTap: () => _signInWithApple(),
            // ),
            const SizedBox(height: 12),
            _buildAuthOption(
              icon: Icons.g_mobiledata,
              title: 'Sign in with Google',
              subtitle: 'Use your Google account',
              onTap: () => _signInWithGoogle(),
            ),
            const SizedBox(height: 12),
            _buildAuthOption(
              icon: LucideIcons.key,
              title: 'Import Seed Phrase',
              subtitle: 'Restore wallet with 12-word phrase',
              onTap: () {
                Navigator.pop(context);
                _showImportWallet('seed');
              },
            ),
            const SizedBox(height: 12),
            _buildAuthOption(
              icon: LucideIcons.fileKey,
              title: 'Import Private Key',
              subtitle: 'Restore wallet with private key',
              onTap: () {
                Navigator.pop(context);
                _showImportWallet('private');
              },
            ),
          ],

          const SizedBox(height: 24),

          // Toggle between sign in and sign up
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.transparent,
                builder: (context) => AuthModal(
                  onSuccess: widget.onSuccess,
                  isSignIn: !widget.isSignIn,
                ),
              );
            },
            child: Text(
              widget.isSignIn
                  ? 'Don\'t have a wallet? Create one'
                  : 'Already have a wallet? Sign in',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white70,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildAuthOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(icon, color: Colors.white, size: 20),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: const TextStyle(fontSize: 13, color: Colors.white60),
                  ),
                ],
              ),
            ),
            const Icon(
              LucideIcons.chevronRight,
              color: Colors.white54,
              size: 18,
            ),
          ],
        ),
      ),
    );
  }

  // void _showEmailAuth() {
  //   showModalBottomSheet(
  //     context: context,
  //     isScrollControlled: true,
  //     backgroundColor: Colors.transparent,
  //     builder: (context) => EmailSignupModal(onSuccess: widget.onSuccess),
  //   );
  // }

  void _showSeedPhraseCreation() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SeedPhraseModal(onSuccess: widget.onSuccess),
    );
  }

  void _showImportWallet(String type) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          ImportWalletModal(importType: type, onSuccess: widget.onSuccess),
    );
  }

  // Future<void> _signInWithApple() async {
  //   // TODO: Implement Apple Sign In
  //   ScaffoldMessenger.of(context).showSnackBar(
  //     const SnackBar(
  //       content: Text('Apple Sign In coming soon!'),
  //       backgroundColor: Colors.orange,
  //     ),
  //   );
  // }

  Future<void> _signInWithGoogle() async {
    // TODO: Implement Google Sign In
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Google Sign In coming soon!'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
