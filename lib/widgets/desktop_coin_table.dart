import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../models/coin_model.dart';
import '../widgets/gradient_button.dart';
import '../pages/responsive_coin_detail_page.dart';

class DesktopCoinTable extends StatelessWidget {
  final List<CoinData> coins;
  final bool isLoading;
  final VoidCallback? onLoadMore;
  final bool isLoadingMore;

  const DesktopCoinTable({
    super.key,
    required this.coins,
    required this.isLoading,
    this.onLoadMore,
    this.isLoadingMore = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // Table Header
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.1),
            border: Border(
              bottom: BorderSide(
                color: Colors.grey.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              const SizedBox(width: 60), // Icon space
              const Expanded(
                flex: 3,
                child: Text(
                  'Name',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
              const Expanded(
                flex: 2,
                child: Text(
                  'Price',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.right,
                ),
              ),
              const Expanded(
                flex: 2,
                child: Text(
                  '24h Change',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.right,
                ),
              ),
              const Expanded(
                flex: 2,
                child: Text(
                  'Market Cap',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.right,
                ),
              ),
              const Expanded(
                flex: 2,
                child: Text(
                  'AI Prediction',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const Expanded(
                flex: 2,
                child: Text(
                  'Action',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
        
        // Table Body
        Expanded(
          child: Scrollbar(
            child: ListView.builder(
              itemCount: coins.length + (onLoadMore != null ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == coins.length && onLoadMore != null) {
                  return Container(
                    padding: const EdgeInsets.all(24),
                    child: Center(
                      child: isLoadingMore
                          ? const CircularProgressIndicator()
                          : GradientButton(
                              text: 'Load More',
                              onPressed: onLoadMore!,
                              icon: LucideIcons.plus,
                            ),
                    ),
                  );
                }

                final coin = coins[index];
                return _buildTableRow(context, coin, index);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTableRow(BuildContext context, CoinData coin, int index) {
    final isPositive = coin.priceChangePercentage24h != null &&
        coin.priceChangePercentage24h! >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;
    
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ResponsiveCoinDetailPage(
              coin: coin,
              symbol: coin.symbol,
            ),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        decoration: BoxDecoration(
          color: index.isEven 
              ? Colors.transparent 
              : Colors.grey.withValues(alpha: 0.05),
          border: Border(
            bottom: BorderSide(
              color: Colors.grey.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            // Rank and Icon
            SizedBox(
              width: 60,
              child: Row(
                children: [
                  Text(
                    '${index + 1}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 12),
                  coin.image != null
                      ? Image.network(
                          coin.image!,
                          width: 24,
                          height: 24,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Icon(
                            Icons.currency_bitcoin,
                            color: Colors.grey[600],
                            size: 24,
                          ),
                        )
                      : Icon(
                          Icons.currency_bitcoin,
                          color: Colors.grey[600],
                          size: 24,
                        ),
                ],
              ),
            ),
            
            // Name and Symbol
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    coin.displayName,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    coin.displaySymbol,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            
            // Price
            Expanded(
              flex: 2,
              child: Text(
                coin.formattedPrice,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                textAlign: TextAlign.right,
              ),
            ),
            
            // 24h Change
            Expanded(
              flex: 2,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Icon(
                    isPositive ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                    color: changeColor,
                    size: 16,
                  ),
                  Text(
                    coin.formattedPriceChange,
                    style: TextStyle(
                      color: changeColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            
            // Market Cap
            Expanded(
              flex: 2,
              child: Text(
                coin.formattedMarketCap,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.right,
              ),
            ),
            
            // AI Prediction
            Expanded(
              flex: 2,
              child: _buildAIPrediction(),
            ),
            
            // Action Button
            Expanded(
              flex: 2,
              child: Center(
                child: SizedBox(
                  width: 100,
                  height: 32,
                  child: GradientButton(
                    text: 'Start Bot',
                    onPressed: () {
                      // Navigate to coin detail or show bot modal
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ResponsiveCoinDetailPage(
                            coin: coin,
                            symbol: coin.symbol,
                          ),
                        ),
                      );
                    },
                    height: 32,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAIPrediction() {
    // Generate a simple AI prediction indicator
    final predictions = ['BULLISH', 'BEARISH', 'NEUTRAL'];
    final colors = [Colors.green, Colors.red, Colors.orange];
    final prediction = predictions[DateTime.now().millisecond % 3];
    final color = colors[DateTime.now().millisecond % 3];
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        prediction,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
