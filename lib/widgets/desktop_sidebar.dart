import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../theme/app_theme.dart';

class DesktopSidebar extends StatelessWidget {
  final int currentPageIndex;
  final Function(int) onPageChanged;
  final bool isSignedIn;
  final VoidCallback onSignIn;

  const DesktopSidebar({
    super.key,
    required this.currentPageIndex,
    required this.onPageChanged,
    required this.isSignedIn,
    required this.onSignIn,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          right: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Logo section
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/images/logo.png',
                  width: 35,
                  height: 35,
                  fit: BoxFit.cover,
                ),
              ],
            ),
          ),
          
          
          // Navigation items
          Expanded(
            child: Column(
              children: [
                if (isSignedIn) ...[
                  _buildNavItem(
                    icon: LucideIcons.circleDot,
                    index: 0,
                    tooltip: 'Coins',
                  ),
                  const SizedBox(height: 8),
                  _buildNavItem(
                    icon: LucideIcons.bot,
                    index: 1,
                    tooltip: 'Bots',
                  ),
                  const SizedBox(height: 8),
                  _buildNavItem(
                    icon: LucideIcons.star,
                    index: 2,
                    tooltip: 'AI',
                  ),
                  const SizedBox(height: 8),
                  _buildNavItem(
                    icon: LucideIcons.arrowUpDown,
                    index: 3,
                    tooltip: 'Trades',
                  ),
                  const SizedBox(height: 8),
                  _buildNavItem(
                    icon: LucideIcons.wallet,
                    index: 4,
                    tooltip: 'Wallet',
                  ),
                ] else ...[
                  _buildNavItem(
                    icon: LucideIcons.circleDot,
                    index: 0,
                    tooltip: 'Coins',
                  ),
                ],
              ],
            ),
          ),
          
          // Sign in section for non-signed in users
          if (!isSignedIn) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Tooltip(
                message: 'Sign In',
                child: InkWell(
                  onTap: onSignIn,
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      gradient: AppTheme.primaryGradient,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      LucideIcons.logIn,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required int index,
    required String tooltip,
  }) {
    final isActive = currentPageIndex == index;
    
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: () => onPageChanged(index),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: 38,
          height: 38,
          decoration: BoxDecoration(
            gradient: isActive ? AppTheme.primaryGradient : null,
            color: isActive ? null : Colors.transparent,
            borderRadius: BorderRadius.circular(100),
          ),
          child: Icon(
            icon,
            color: isActive ? Colors.white : Colors.grey[600],
            size: 20,
          ),
        ),
      ),
    );
  }
}
