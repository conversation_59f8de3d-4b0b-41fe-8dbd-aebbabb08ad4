import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../theme/app_theme.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.showBackButton = false,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: AppBar(
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: showBackButton
            ? IconButton(
                icon: const Icon(LucideIcons.chevronLeft, color: Colors.white),
                onPressed: onBackPressed ?? () => Navigator.pop(context),
              )
            : leading,
        actions: actions?.map((action) {
          if (action is IconButton) {
            return IconButton(
              icon: Icon(
                (action.icon as Icon).icon,
                color: Colors.white,
              ),
              onPressed: action.onPressed,
            );
          }
          return action;
        }).toList(),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class CustomSliverAppBar extends StatelessWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final double expandedHeight;
  final Widget? flexibleSpace;

  const CustomSliverAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.showBackButton = false,
    this.onBackPressed,
    this.expandedHeight = 120.0,
    this.flexibleSpace,
  });

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      expandedHeight: expandedHeight,
      floating: false,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
          ),
          child: flexibleSpace,
        ),
      ),
      backgroundColor: Colors.transparent,
      leading: showBackButton
          ? IconButton(
              icon: const Icon(LucideIcons.chevronLeft, color: Colors.white),
              onPressed: onBackPressed ?? () => Navigator.pop(context),
            )
          : leading,
      actions: actions?.map((action) {
        if (action is IconButton) {
          return IconButton(
            icon: Icon(
              (action.icon as Icon).icon,
              color: Colors.white,
            ),
            onPressed: action.onPressed,
          );
        }
        return action;
      }).toList(),
      iconTheme: const IconThemeData(color: Colors.white),
    );
  }
}
