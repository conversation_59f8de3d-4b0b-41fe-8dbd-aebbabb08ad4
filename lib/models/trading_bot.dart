import 'coin_model.dart';

enum IndicatorType {
  rsi,
  macd,
  sma,
  ema,
  bollinger,
  stochastic,
  williams,
  adx,
}

enum OrderType {
  buy,
  sell,
}

class TradingIndicator {
  final IndicatorType type;
  final Map<String, dynamic> parameters;
  final double buyThreshold;
  final double sellThreshold;
  final bool isEnabled;

  TradingIndicator({
    required this.type,
    required this.parameters,
    required this.buyThreshold,
    required this.sellThreshold,
    this.isEnabled = true,
  });

  String get name {
    switch (type) {
      case IndicatorType.rsi:
        return 'RSI';
      case IndicatorType.macd:
        return 'MACD';
      case IndicatorType.sma:
        return 'SMA';
      case IndicatorType.ema:
        return 'EMA';
      case IndicatorType.bollinger:
        return 'Bollinger Bands';
      case IndicatorType.stochastic:
        return 'Stochastic';
      case IndicatorType.williams:
        return 'Williams %R';
      case IndicatorType.adx:
        return 'ADX';
    }
  }

  String get description {
    switch (type) {
      case IndicatorType.rsi:
        return 'Relative Strength Index - Momentum oscillator';
      case IndicatorType.macd:
        return 'Moving Average Convergence Divergence';
      case IndicatorType.sma:
        return 'Simple Moving Average';
      case IndicatorType.ema:
        return 'Exponential Moving Average';
      case IndicatorType.bollinger:
        return 'Bollinger Bands - Volatility indicator';
      case IndicatorType.stochastic:
        return 'Stochastic Oscillator';
      case IndicatorType.williams:
        return 'Williams %R - Momentum indicator';
      case IndicatorType.adx:
        return 'Average Directional Index - Trend strength';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString(),
      'parameters': parameters,
      'buyThreshold': buyThreshold,
      'sellThreshold': sellThreshold,
      'isEnabled': isEnabled,
    };
  }

  factory TradingIndicator.fromJson(Map<String, dynamic> json) {
    return TradingIndicator(
      type: IndicatorType.values.firstWhere(
        (e) => e.toString() == json['type'],
      ),
      parameters: json['parameters'],
      buyThreshold: json['buyThreshold'],
      sellThreshold: json['sellThreshold'],
      isEnabled: json['isEnabled'] ?? true,
    );
  }
}

class TradeOrder {
  final String id;
  final OrderType type;
  final double amount;
  final double price;
  final DateTime timestamp;
  final String reason;

  TradeOrder({
    required this.id,
    required this.type,
    required this.amount,
    required this.price,
    required this.timestamp,
    required this.reason,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString(),
      'amount': amount,
      'price': price,
      'timestamp': timestamp.toIso8601String(),
      'reason': reason,
    };
  }

  factory TradeOrder.fromJson(Map<String, dynamic> json) {
    return TradeOrder(
      id: json['id'],
      type: OrderType.values.firstWhere(
        (e) => e.toString() == json['type'],
      ),
      amount: json['amount'],
      price: json['price'],
      timestamp: DateTime.parse(json['timestamp']),
      reason: json['reason'],
    );
  }
}

class TradingBot {
  final String id;
  final String name;
  final CoinData coin;
  final List<TradingIndicator> indicators;
  final double investmentAmount;
  final double stopLoss;
  final double takeProfit;
  bool isActive;
  final DateTime createdAt;
  final List<TradeOrder> tradeHistory;

  TradingBot({
    required this.id,
    required this.name,
    required this.coin,
    required this.indicators,
    required this.investmentAmount,
    required this.stopLoss,
    required this.takeProfit,
    this.isActive = false,
    required this.createdAt,
    List<TradeOrder>? tradeHistory,
  }) : tradeHistory = tradeHistory ?? [];

  int get totalTrades => tradeHistory.length;

  double get winRate {
    if (tradeHistory.isEmpty) return 0.0;
    
    final buyOrders = tradeHistory.where((order) => order.type == OrderType.buy).toList();
    final sellOrders = tradeHistory.where((order) => order.type == OrderType.sell).toList();
    
    if (buyOrders.isEmpty || sellOrders.isEmpty) return 0.0;
    
    int winningTrades = 0;
    for (int i = 0; i < buyOrders.length && i < sellOrders.length; i++) {
      if (sellOrders[i].price > buyOrders[i].price) {
        winningTrades++;
      }
    }
    
    return (winningTrades / buyOrders.length) * 100;
  }

  double get profitLoss {
    if (tradeHistory.isEmpty) return 0.0;
    
    double totalProfit = 0.0;
    final buyOrders = tradeHistory.where((order) => order.type == OrderType.buy).toList();
    final sellOrders = tradeHistory.where((order) => order.type == OrderType.sell).toList();
    
    for (int i = 0; i < buyOrders.length && i < sellOrders.length; i++) {
      final profit = ((sellOrders[i].price - buyOrders[i].price) / buyOrders[i].price) * 100;
      totalProfit += profit;
    }
    
    return totalProfit;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'coin': coin.toJson(),
      'indicators': indicators.map((i) => i.toJson()).toList(),
      'investmentAmount': investmentAmount,
      'stopLoss': stopLoss,
      'takeProfit': takeProfit,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'tradeHistory': tradeHistory.map((t) => t.toJson()).toList(),
    };
  }

  factory TradingBot.fromJson(Map<String, dynamic> json) {
    return TradingBot(
      id: json['id'],
      name: json['name'],
      coin: CoinData.fromJson(json['coin']),
      indicators: (json['indicators'] as List)
          .map((i) => TradingIndicator.fromJson(i))
          .toList(),
      investmentAmount: json['investmentAmount'],
      stopLoss: json['stopLoss'],
      takeProfit: json['takeProfit'],
      isActive: json['isActive'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      tradeHistory: (json['tradeHistory'] as List?)
          ?.map((t) => TradeOrder.fromJson(t))
          .toList(),
    );
  }
}
