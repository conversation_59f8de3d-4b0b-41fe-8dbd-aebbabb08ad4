class CoinData {
  final String id;
  final String symbol;
  final String name;
  final String? image;
  final double? currentPrice;
  final double? marketCap;
  final int? marketCapRank;
  final double? fullyDilutedValuation;
  final double? totalVolume;
  final double? high24h;
  final double? low24h;
  final double? priceChange24h;
  final double? priceChangePercentage24h;
  final double? marketCapChange24h;
  final double? marketCapChangePercentage24h;
  final double? circulatingSupply;
  final double? totalSupply;
  final double? maxSupply;
  final double? ath;
  final double? athChangePercentage;
  final String? athDate;
  final double? atl;
  final double? atlChangePercentage;
  final String? atlDate;
  final String? lastUpdated;

  CoinData({
    required this.id,
    required this.symbol,
    required this.name,
    this.image,
    this.currentPrice,
    this.marketCap,
    this.marketCapRank,
    this.fullyDilutedValuation,
    this.totalVolume,
    this.high24h,
    this.low24h,
    this.priceChange24h,
    this.priceChangePercentage24h,
    this.marketCapChange24h,
    this.marketCapChangePercentage24h,
    this.circulatingSupply,
    this.totalSupply,
    this.maxSupply,
    this.ath,
    this.athChangePercentage,
    this.athDate,
    this.atl,
    this.atlChangePercentage,
    this.atlDate,
    this.lastUpdated,
  });

  factory CoinData.fromJson(Map<String, dynamic> json) {
    return CoinData(
      id: json['id'] ?? json['address'] ?? '',
      symbol: json['symbol'] ?? '',
      name: json['name'] ?? '',
      image: json['image'] ?? json['logoURI'],
      currentPrice:
          json['current_price']?.toDouble() ?? json['price']?.toDouble(),
      marketCap: json['market_cap']?.toDouble() ?? json['mc']?.toDouble(),
      marketCapRank: json['market_cap_rank']?.toInt(),
      fullyDilutedValuation: json['fully_diluted_valuation']?.toDouble(),
      totalVolume:
          json['total_volume']?.toDouble() ?? json['v24hUSD']?.toDouble(),
      high24h: json['high_24h']?.toDouble(),
      low24h: json['low_24h']?.toDouble(),
      priceChange24h: json['price_change_24h']?.toDouble(),
      priceChangePercentage24h:
          json['price_change_percentage_24h']?.toDouble() ??
          json['priceChange24hPercent']?.toDouble(),
      marketCapChange24h: json['market_cap_change_24h']?.toDouble(),
      marketCapChangePercentage24h: json['market_cap_change_percentage_24h']
          ?.toDouble(),
      circulatingSupply: json['circulating_supply']?.toDouble(),
      totalSupply: json['total_supply']?.toDouble(),
      maxSupply: json['max_supply']?.toDouble(),
      ath: json['ath']?.toDouble(),
      athChangePercentage: json['ath_change_percentage']?.toDouble(),
      athDate: json['ath_date'],
      atl: json['atl']?.toDouble(),
      atlChangePercentage: json['atl_change_percentage']?.toDouble(),
      atlDate: json['atl_date'],
      lastUpdated: json['last_updated'],
    );
  }

  // Factory for Birdeye API response
  factory CoinData.fromBirdeyeJson(Map<String, dynamic> json) {
    return CoinData(
      id: json['address'] ?? '',
      symbol: json['symbol'] ?? '',
      name: json['name'] ?? '',
      image: json['logoURI'],
      currentPrice: json['price']?.toDouble(),
      marketCap: json['mc']?.toDouble(),
      marketCapRank: null,
      fullyDilutedValuation: null,
      totalVolume: json['v24hUSD']?.toDouble(),
      high24h: null,
      low24h: null,
      priceChange24h: json['priceChange24h']?.toDouble(),
      priceChangePercentage24h: json['priceChange24hPercent']?.toDouble(),
      marketCapChange24h: null,
      marketCapChangePercentage24h: null,
      circulatingSupply: null,
      totalSupply: null,
      maxSupply: null,
      ath: null,
      athChangePercentage: null,
      athDate: null,
      atl: null,
      atlChangePercentage: null,
      atlDate: null,
      lastUpdated: null,
    );
  }

  String get displayName => name;
  String get displaySymbol => symbol.toUpperCase();

  String get formattedPrice {
    if (currentPrice == null) return 'N/A';
    if (currentPrice! < 0.01) {
      return '\$${currentPrice!.toStringAsFixed(6)}';
    } else if (currentPrice! < 1) {
      return '\$${currentPrice!.toStringAsFixed(4)}';
    }
    return '\$${currentPrice!.toStringAsFixed(2)}';
  }

  String get formattedPriceChange {
    if (priceChangePercentage24h == null) return 'N/A';
    final sign = priceChangePercentage24h! >= 0 ? '+' : '';
    return '$sign${priceChangePercentage24h!.toStringAsFixed(2)}%';
  }

  String get formattedVolume {
    if (totalVolume == null) return 'N/A';
    if (totalVolume! >= 1000000000) {
      return '\$${(totalVolume! / 1000000000).toStringAsFixed(2)}B';
    } else if (totalVolume! >= 1000000) {
      return '\$${(totalVolume! / 1000000).toStringAsFixed(2)}M';
    } else if (totalVolume! >= 1000) {
      return '\$${(totalVolume! / 1000).toStringAsFixed(2)}K';
    }
    return '\$${totalVolume!.toStringAsFixed(2)}';
  }

  String get formattedMarketCap {
    if (marketCap == null) return 'N/A';
    if (marketCap! >= 1000000000) {
      return '\$${(marketCap! / 1000000000).toStringAsFixed(2)}B';
    } else if (marketCap! >= 1000000) {
      return '\$${(marketCap! / 1000000).toStringAsFixed(2)}M';
    } else if (marketCap! >= 1000) {
      return '\$${(marketCap! / 1000).toStringAsFixed(2)}K';
    }
    return '\$${marketCap!.toStringAsFixed(2)}';
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'symbol': symbol,
      'name': name,
      'image': image,
      'current_price': currentPrice,
      'market_cap': marketCap,
      'market_cap_rank': marketCapRank,
      'fully_diluted_valuation': fullyDilutedValuation,
      'total_volume': totalVolume,
      'high_24h': high24h,
      'low_24h': low24h,
      'price_change_24h': priceChange24h,
      'price_change_percentage_24h': priceChangePercentage24h,
      'market_cap_change_24h': marketCapChange24h,
      'market_cap_change_percentage_24h': marketCapChangePercentage24h,
      'circulating_supply': circulatingSupply,
      'total_supply': totalSupply,
      'max_supply': maxSupply,
      'ath': ath,
      'ath_change_percentage': athChangePercentage,
      'ath_date': athDate,
      'atl': atl,
      'atl_change_percentage': atlChangePercentage,
      'atl_date': atlDate,
      'last_updated': lastUpdated,
    };
  }
}
