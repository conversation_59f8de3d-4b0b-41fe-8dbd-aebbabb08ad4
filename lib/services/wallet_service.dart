import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/wallet_model.dart';

class WalletService extends ChangeNotifier {
  static final WalletService _instance = WalletService._internal();
  factory WalletService() => _instance;
  WalletService._internal();

  List<WalletModel> _wallets = [];
  WalletModel? _activeWallet;
  List<Transaction> _transactions = [];

  List<WalletModel> get wallets => List.unmodifiable(_wallets);
  WalletModel? get activeWallet => _activeWallet;
  List<Transaction> get transactions => List.unmodifiable(_transactions);

  // Initialize service and load saved wallets
  Future<void> initialize() async {
    await _loadWallets();
    await _loadTransactions();
    _generateMockData();
  }

  // Create a new paper wallet
  Future<WalletModel> createPaperWallet({
    required String name,
    required double initialBalance,
  }) async {
    final wallet = WalletModel(
      id: _generateId(),
      name: name,
      type: WalletType.paper,
      balance: initialBalance,
      createdAt: DateTime.now(),
      isActive: _wallets.isEmpty, // First wallet is active by default
    );

    _wallets.add(wallet);
    if (_wallets.length == 1) {
      _activeWallet = wallet;
    }

    await _saveWallets();
    notifyListeners();
    return wallet;
  }

  // Create a new Solana wallet with generated keys
  Future<WalletModel> createSolanaWallet({required String name}) async {
    // In a real app, you would use proper Solana key generation
    final publicKey = _generateMockSolanaAddress();
    final privateKey = _generateMockPrivateKey();

    final wallet = WalletModel(
      id: _generateId(),
      name: name,
      type: WalletType.solana,
      address: publicKey,
      privateKey: privateKey,
      balance: 0.0, // Start with 0 balance
      createdAt: DateTime.now(),
      isActive: _wallets.isEmpty,
    );

    _wallets.add(wallet);
    if (_wallets.length == 1) {
      _activeWallet = wallet;
    }

    await _saveWallets();
    notifyListeners();
    return wallet;
  }

  // Set active wallet
  Future<void> setActiveWallet(String walletId) async {
    final walletIndex = _wallets.indexWhere((w) => w.id == walletId);
    if (walletIndex != -1) {
      // Update all wallets to inactive
      _wallets = _wallets.map((w) => w.copyWith(isActive: false)).toList();
      // Set the selected wallet as active
      _wallets[walletIndex] = _wallets[walletIndex].copyWith(isActive: true);
      _activeWallet = _wallets[walletIndex];

      await _saveWallets();
      notifyListeners();
    }
  }

  // Delete a wallet
  Future<void> deleteWallet(String walletId) async {
    final walletIndex = _wallets.indexWhere((w) => w.id == walletId);
    if (walletIndex != -1) {
      final wasActive = _wallets[walletIndex].isActive;
      _wallets.removeAt(walletIndex);

      // If deleted wallet was active, set first wallet as active
      if (wasActive && _wallets.isNotEmpty) {
        _wallets[0] = _wallets[0].copyWith(isActive: true);
        _activeWallet = _wallets[0];
      } else if (_wallets.isEmpty) {
        _activeWallet = null;
      }

      await _saveWallets();
      notifyListeners();
    }
  }

  // Get transactions for a specific wallet
  List<Transaction> getWalletTransactions(String walletId) {
    return _transactions.where((t) => t.walletId == walletId).toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  // Get portfolio data for active wallet
  PortfolioData getPortfolioData() {
    if (_activeWallet == null) {
      return const PortfolioData(
        totalValue: 0,
        change24h: 0,
        changePercent24h: 0,
        holdings: [],
        history: [],
      );
    }

    // Mock portfolio data
    final holdings = [
      TokenHolding(
        symbol: 'SOL',
        name: 'Solana',
        balance: _activeWallet!.balance,
        price: 142.50,
        value: _activeWallet!.balance * 142.50,
        change24h: 5.20,
        imageUrl:
            'https://assets.coingecko.com/coins/images/4128/large/solana.png',
      ),
      if (_activeWallet!.type != WalletType.paper) ...[
        const TokenHolding(
          symbol: 'USDC',
          name: 'USD Coin',
          balance: 150.0,
          price: 1.00,
          value: 150.0,
          change24h: 0.01,
          imageUrl:
              'https://assets.coingecko.com/coins/images/6319/large/USD_Coin_icon.png',
        ),
      ],
    ];

    final totalValue = holdings.fold(
      0.0,
      (sum, holding) => sum + holding.value,
    );
    final totalChange24h = holdings.fold(
      0.0,
      (sum, holding) => sum + (holding.change24h * holding.balance),
    );

    return PortfolioData(
      totalValue: totalValue,
      change24h: totalChange24h,
      changePercent24h: totalValue > 0
          ? (totalChange24h / totalValue) * 100
          : 0,
      holdings: holdings,
      history: _generateMockHistory(totalValue),
    );
  }

  // Private methods
  String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString() +
        Random().nextInt(1000).toString();
  }

  String _generateMockSolanaAddress() {
    const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz123456789';
    final random = Random();
    return List.generate(
      44,
      (index) => chars[random.nextInt(chars.length)],
    ).join();
  }

  String _generateMockPrivateKey() {
    const chars = '0123456789abcdef';
    final random = Random();
    return List.generate(
      64,
      (index) => chars[random.nextInt(chars.length)],
    ).join();
  }

  List<PortfolioHistoryPoint> _generateMockHistory(double currentValue) {
    final history = <PortfolioHistoryPoint>[];
    final now = DateTime.now();

    for (int i = 30; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final variance = (Random().nextDouble() - 0.5) * 0.1; // ±5% variance
      final value = currentValue * (1 + variance);
      history.add(PortfolioHistoryPoint(timestamp: date, value: value));
    }

    return history;
  }

  void _generateMockData() {
    if (_activeWallet != null && _transactions.isEmpty) {
      // Generate some mock transactions
      final mockTransactions = [
        Transaction(
          id: _generateId(),
          walletId: _activeWallet!.id,
          type: TransactionType.receive,
          amount: 1.5,
          toAddress: _activeWallet!.address,
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
          hash:
              '5j7k8l9m0n1o2p3q4r5s6t7u8v9w0x1y2z3a4b5c6d7e8f9g0h1i2j3k4l5m6n7o8p9q0r1s2t3u4v5w6x7y8z9',
          status: TransactionStatus.confirmed,
        ),
        Transaction(
          id: _generateId(),
          walletId: _activeWallet!.id,
          type: TransactionType.send,
          amount: 0.5,
          fromAddress: _activeWallet!.address,
          timestamp: DateTime.now().subtract(const Duration(days: 1)),
          hash:
              '9q0r1s2t3u4v5w6x7y8z9a0b1c2d3e4f5g6h7i8j9k0l1m2n3o4p5q6r7s8t9u0v1w2x3y4z5a6b7c8d9e0f1g2h3i4j5',
          status: TransactionStatus.confirmed,
        ),
      ];

      _transactions.addAll(mockTransactions);
    }
  }

  Future<void> _saveWallets() async {
    final prefs = await SharedPreferences.getInstance();
    final walletsJson = _wallets.map((w) => w.toJson()).toList();
    await prefs.setString('wallets', jsonEncode(walletsJson));

    if (_activeWallet != null) {
      await prefs.setString('active_wallet_id', _activeWallet!.id);
    }
  }

  Future<void> _loadWallets() async {
    final prefs = await SharedPreferences.getInstance();
    final walletsString = prefs.getString('wallets');
    final activeWalletId = prefs.getString('active_wallet_id');

    if (walletsString != null) {
      final List<dynamic> walletsJson = jsonDecode(walletsString);
      _wallets = walletsJson.map((json) => WalletModel.fromJson(json)).toList();

      if (activeWalletId != null) {
        try {
          _activeWallet = _wallets.firstWhere((w) => w.id == activeWalletId);
        } catch (e) {
          _activeWallet = _wallets.isNotEmpty ? _wallets.first : null;
        }
      }
    }
  }

  Future<void> _loadTransactions() async {
    final prefs = await SharedPreferences.getInstance();
    final transactionsString = prefs.getString('transactions');

    if (transactionsString != null) {
      final List<dynamic> transactionsJson = jsonDecode(transactionsString);
      _transactions = transactionsJson
          .map((json) => Transaction.fromJson(json))
          .toList();
    }
  }
}
