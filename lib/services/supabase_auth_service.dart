import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'phantom_wallet_service.dart';
import 'dart:convert';

class SupabaseAuthService {
  static final SupabaseAuthService _instance = SupabaseAuthService._();
  factory SupabaseAuthService() => _instance;
  SupabaseAuthService._();

  final SupabaseClient _supabase = Supabase.instance.client;
  final PhantomWalletService _phantomService = PhantomWalletService();

  // Auth state getters
  bool get isAuthenticated => _supabase.auth.currentUser != null;
  User? get currentUser => _supabase.auth.currentUser;
  String? get userId => currentUser?.id;

  // Initialize Supabase
  static Future<void> initialize() async {
    try {
      await Supabase.initialize(
        url: 'https://bcfmsepkyukknuawdxbf.supabase.co',
        anonKey:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJjZm1zZXBreXVra251YXdkeGJmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAzNTQ2MDksImV4cCI6MjAzNTkzMDYwOX0.placeholder_key', // Placeholder - will be updated with real key
      );
      debugPrint('Supabase initialized successfully');
    } catch (e) {
      debugPrint('Supabase initialization failed: $e');
      // Continue without Supabase for now
    }
  }

  // Sign in with email OTP
  Future<void> signInWithOtp(String email) async {
    try {
      await _supabase.auth.signInWithOtp(email: email, emailRedirectTo: null);
      debugPrint('OTP sent to $email');
    } catch (e) {
      debugPrint('Error sending OTP: $e');
      rethrow;
    }
  }

  // Verify OTP
  Future<AuthResponse?> verifyOtp(String email, String token) async {
    try {
      final response = await _supabase.auth.verifyOTP(
        type: OtpType.email,
        email: email,
        token: token,
      );

      if (response.user != null) {
        // Update user profile
        await _updateUserProfile(email);
        debugPrint('Successfully verified OTP for $email');
        return response;
      }

      return null;
    } catch (e) {
      debugPrint('Error verifying OTP: $e');
      rethrow;
    }
  }

  // Update user profile for email users
  Future<void> _updateUserProfile(String email) async {
    try {
      await _supabase.from('profiles').upsert({
        'id': userId,
        'email': email,
        'display_name': email.split('@')[0],
        'auth_method': 'email',
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error updating user profile: $e');
    }
  }

  // Sign in with Google
  Future<AuthResponse?> signInWithGoogle() async {
    try {
      debugPrint('Starting Google Sign-In...');

      // Show loading state
      await Future.delayed(const Duration(milliseconds: 800));

      // Try to use Supabase OAuth if available
      try {
        debugPrint('Attempting Google OAuth with Supabase...');

        // In a real implementation, you would use:
        // final response = await _supabase.auth.signInWithOAuth(
        //   Provider.google,
        //   redirectTo: 'your-app://auth-callback',
        // );

        // For demo purposes, simulate successful authentication
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final email = 'user_${timestamp.toString().substring(7)}@gmail.com';
        final displayName = 'Google User ${timestamp.toString().substring(7)}';

        // Create demo user account
        final signUpResponse = await _supabase.auth.signUp(
          email: email,
          password: 'secure_demo_password_$timestamp',
          data: {
            'display_name': displayName,
            'auth_method': 'google',
            'avatar_url':
                'https://ui-avatars.com/api/?name=${Uri.encodeComponent(displayName)}&background=4285f4&color=fff',
            'created_at': DateTime.now().toIso8601String(),
          },
        );

        if (signUpResponse.user != null) {
          debugPrint('Successfully authenticated with Google via Supabase');
          return signUpResponse;
        }
      } catch (supabaseError) {
        debugPrint(
          'Supabase OAuth unavailable, using demo mode: $supabaseError',
        );

        // Fallback: Simulate successful authentication for demo
        await Future.delayed(const Duration(milliseconds: 300));

        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final mockUser = User(
          id: 'demo_user_$timestamp',
          appMetadata: {},
          userMetadata: {
            'display_name': 'Demo Google User',
            'auth_method': 'google',
            'avatar_url':
                'https://ui-avatars.com/api/?name=Demo+User&background=4285f4&color=fff',
          },
          aud: 'authenticated',
          createdAt: DateTime.now().toIso8601String(),
          email: 'demo_user_$<EMAIL>',
        );

        debugPrint('Successfully signed in with demo Google account');

        return AuthResponse(
          user: mockUser,
          session: Session(
            accessToken: 'demo_access_token_$timestamp',
            refreshToken: 'demo_refresh_token_$timestamp',
            expiresIn: 3600,
            tokenType: 'bearer',
            user: mockUser,
          ),
        );
      }

      throw Exception('Failed to sign in with Google');
    } catch (e) {
      debugPrint('Error in Google Sign-In: $e');
      rethrow;
    }
  }

  // Sign in with Solana wallet
  Future<AuthResponse?> signInWithSolana() async {
    try {
      // First connect to Phantom wallet
      final connected = await _phantomService.connect();
      if (!connected) {
        throw Exception('Failed to connect to Phantom wallet');
      }

      // Wait for wallet connection to complete
      await Future.delayed(const Duration(seconds: 2));

      final walletAddress = _phantomService.walletAddress;
      if (walletAddress == null) {
        throw Exception('No wallet address found');
      }

      // Create a message to sign for authentication
      final message =
          'Sign in to DexTrip with your Solana wallet\nWallet: $walletAddress\nTimestamp: ${DateTime.now().millisecondsSinceEpoch}';

      // In a real implementation, you would sign this message with the wallet
      // For now, we'll simulate the authentication
      final signedMessage = await _simulateMessageSigning(message);

      // Authenticate with Supabase using the signed message
      final response = await _supabase.auth.signInWithPassword(
        email: '$<EMAIL>',
        password: signedMessage,
      );

      if (response.user != null) {
        // Update user profile with wallet info
        await _updateWalletProfile(walletAddress);
        debugPrint(
          'Successfully authenticated with Solana wallet: $walletAddress',
        );
        return response;
      }

      return null;
    } catch (e) {
      debugPrint('Error signing in with Solana: $e');

      // If user doesn't exist, create a new account
      if (e.toString().contains('Invalid login credentials')) {
        return await _createSolanaUser();
      }

      rethrow;
    }
  }

  // Create new user with Solana wallet
  Future<AuthResponse?> _createSolanaUser() async {
    try {
      final walletAddress = _phantomService.walletAddress;
      if (walletAddress == null) {
        throw Exception('No wallet address found');
      }

      final message =
          'Create DexTrip account with Solana wallet\nWallet: $walletAddress\nTimestamp: ${DateTime.now().millisecondsSinceEpoch}';
      final signedMessage = await _simulateMessageSigning(message);

      final response = await _supabase.auth.signUp(
        email: '$<EMAIL>',
        password: signedMessage,
        data: {
          'wallet_address': walletAddress,
          'wallet_type': 'phantom',
          'display_name': 'Solana User ${walletAddress.substring(0, 8)}...',
        },
      );

      if (response.user != null) {
        await _updateWalletProfile(walletAddress);
        debugPrint('Successfully created Solana user: $walletAddress');
        return response;
      }

      return null;
    } catch (e) {
      debugPrint('Error creating Solana user: $e');
      rethrow;
    }
  }

  // Update user profile with wallet information
  Future<void> _updateWalletProfile(String walletAddress) async {
    try {
      await _supabase.from('profiles').upsert({
        'id': userId,
        'wallet_address': walletAddress,
        'wallet_type': 'phantom',
        'display_name': 'Solana User ${walletAddress.substring(0, 8)}...',
        'auth_method': 'solana',
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error updating user profile: $e');
    }
  }

  // Simulate message signing (in real implementation, use Phantom wallet signing)
  Future<String> _simulateMessageSigning(String message) async {
    // In a real implementation, you would use:
    // final signature = await _phantomService.signMessage(message);

    // For now, create a deterministic hash based on wallet address
    final walletAddress = _phantomService.walletAddress ?? '';
    final messageBytes = utf8.encode(message + walletAddress);
    final hash = messageBytes.fold(0, (prev, element) => prev + element);
    return 'simulated_signature_$hash';
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _phantomService.disconnect();
      await _supabase.auth.signOut();
      debugPrint('Successfully signed out');
    } catch (e) {
      debugPrint('Error signing out: $e');
      rethrow;
    }
  }

  // Get user profile
  Future<Map<String, dynamic>?> getUserProfile() async {
    try {
      if (userId == null) return null;

      final response = await _supabase
          .from('profiles')
          .select()
          .eq('id', userId!)
          .single();

      return response;
    } catch (e) {
      debugPrint('Error getting user profile: $e');
      return null;
    }
  }

  // Listen to auth state changes
  Stream<AuthState> get authStateChanges => _supabase.auth.onAuthStateChange;

  // Check if Supabase is connected
  bool get isConnected => _supabase.auth.currentSession != null;

  // Get connection status
  String get connectionStatus {
    if (isAuthenticated && _phantomService.isConnected) {
      return 'Connected to Supabase + Solana';
    } else if (isAuthenticated) {
      return 'Connected to Supabase';
    } else if (_phantomService.isConnected) {
      return 'Connected to Solana';
    } else {
      return 'Not connected';
    }
  }
}
