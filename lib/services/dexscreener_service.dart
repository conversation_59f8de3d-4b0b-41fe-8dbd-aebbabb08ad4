import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/coin_model.dart';

class CoinGeckoService {
  static const String baseUrl = 'https://api.coingecko.com/api/v3';

  // Get trending coins with pagination
  static Future<List<CoinData>> getTrendingCoins({
    int page = 1,
    int perPage = 25,
  }) async {
    try {
      final response = await http.get(
        Uri.parse(
          '$baseUrl/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=$perPage&page=$page&sparkline=false',
        ),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((coin) => CoinData.fromJson(coin)).toList();
      } else {
        throw Exception(
          'Failed to load trending coins: ${response.statusCode}',
        );
      }
    } catch (e) {
      throw Exception('Error fetching trending coins: $e');
    }
  }

  // Search coins by name, symbol, or token address
  static Future<List<CoinData>> searchCoins(String query) async {
    if (query.isEmpty) return getTrendingCoins();

    try {
      // Check if query looks like a token address
      if (_isTokenAddress(query)) {
        return await _searchByTokenAddress(query);
      }

      // Search by name/symbol using CoinGecko search endpoint
      return await _searchByNameOrSymbol(query);
    } catch (e) {
      throw Exception('Error searching coins: $e');
    }
  }

  // Check if query looks like a token address
  static bool _isTokenAddress(String query) {
    // Ethereum address (0x + 40 hex chars)
    if (query.startsWith('0x') && query.length == 42) {
      return true;
    }
    // Solana address (base58, 32-44 chars)
    final solanaRegex = RegExp(r'^[1-9A-HJ-NP-Za-km-z]{32,44}$');
    return solanaRegex.hasMatch(query) && !query.startsWith('0x');
  }

  // Search by token address
  static Future<List<CoinData>> _searchByTokenAddress(String address) async {
    try {
      String endpoint;

      // Determine the blockchain based on address format
      if (address.startsWith('0x') && address.length == 42) {
        // Ethereum address
        endpoint = '$baseUrl/coins/ethereum/contract/$address';
      } else {
        // Assume Solana address
        endpoint = '$baseUrl/coins/solana/contract/$address';
      }

      final response = await http.get(
        Uri.parse(endpoint),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final coinData = json.decode(response.body);

        // Get market data for this coin
        final coinId = coinData['id'];
        final marketResponse = await http.get(
          Uri.parse(
            '$baseUrl/coins/markets?vs_currency=usd&ids=$coinId&sparkline=false',
          ),
          headers: {'Content-Type': 'application/json'},
        );

        if (marketResponse.statusCode == 200) {
          final List<dynamic> marketData = json.decode(marketResponse.body);
          if (marketData.isNotEmpty) {
            return [CoinData.fromJson(marketData.first)];
          }
        }
      }

      return [];
    } catch (e) {
      return [];
    }
  }

  // Search by name or symbol
  static Future<List<CoinData>> _searchByNameOrSymbol(String query) async {
    try {
      // First get search results to find coin IDs
      final searchResponse = await http.get(
        Uri.parse('$baseUrl/search?query=${Uri.encodeComponent(query)}'),
        headers: {'Content-Type': 'application/json'},
      );

      if (searchResponse.statusCode == 200) {
        final searchData = json.decode(searchResponse.body);
        final coins = searchData['coins'] as List;

        if (coins.isEmpty) {
          // If no results, try trending coins and filter locally
          final trendingCoins = await getTrendingCoins();
          return trendingCoins.where((coin) {
            final queryLower = query.toLowerCase();
            return coin.name.toLowerCase().contains(queryLower) ||
                coin.symbol.toLowerCase().contains(queryLower);
          }).toList();
        }

        // Get detailed market data for found coins
        final coinIds = coins.take(20).map((coin) => coin['id']).join(',');
        final marketResponse = await http.get(
          Uri.parse(
            '$baseUrl/coins/markets?vs_currency=usd&ids=$coinIds&order=market_cap_desc&sparkline=false',
          ),
          headers: {'Content-Type': 'application/json'},
        );

        if (marketResponse.statusCode == 200) {
          final List<dynamic> marketData = json.decode(marketResponse.body);
          return marketData.map((coin) => CoinData.fromJson(coin)).toList();
        }
      }

      return [];
    } catch (e) {
      // Fallback to trending coins with local filtering
      try {
        final trendingCoins = await getTrendingCoins();
        return trendingCoins.where((coin) {
          final queryLower = query.toLowerCase();
          return coin.name.toLowerCase().contains(queryLower) ||
              coin.symbol.toLowerCase().contains(queryLower);
        }).toList();
      } catch (e2) {
        throw Exception('Search failed: $e');
      }
    }
  }
}
