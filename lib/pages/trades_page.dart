import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';

enum TradeStatus { live, watching, archive }

enum TradeType { buy, sell }

class TradeItem {
  final String id;
  final String tokenName;
  final String tokenSymbol;
  final String tokenImage;
  final TradeType type;
  final double amount;
  final double price;
  final double currentPrice;
  final double profitLoss;
  final double profitLossPercentage;
  final DateTime timestamp;
  final TradeStatus status;
  final bool isActive;

  TradeItem({
    required this.id,
    required this.tokenName,
    required this.tokenSymbol,
    required this.tokenImage,
    required this.type,
    required this.amount,
    required this.price,
    required this.currentPrice,
    required this.profitLoss,
    required this.profitLossPercentage,
    required this.timestamp,
    required this.status,
    required this.isActive,
  });
}

class TradesPage extends StatefulWidget {
  const TradesPage({super.key});

  @override
  State<TradesPage> createState() => _TradesPageState();
}

class _TradesPageState extends State<TradesPage> with TickerProviderStateMixin {
  String _selectedCategory = 'Live';
  List<TradeItem> _allTrades = [];
  List<TradeItem> _filteredTrades = [];
  Timer? _priceUpdateTimer;
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    _loadDummyTrades();
    _filterTrades();
    _startPriceUpdates();
  }

  @override
  void dispose() {
    _priceUpdateTimer?.cancel();
    super.dispose();
  }

  void _loadDummyTrades() {
    _allTrades = [
      // Live trades
      TradeItem(
        id: '1',
        tokenName: 'Bonk',
        tokenSymbol: 'BONK',
        tokenImage: 'assets/images/bonk.png',
        type: TradeType.buy,
        amount: 0.5,
        price: 0.000012,
        currentPrice: 0.000014,
        profitLoss: 0.08,
        profitLossPercentage: 16.7,
        timestamp: DateTime.now().subtract(const Duration(minutes: 15)),
        status: TradeStatus.live,
        isActive: true,
      ),
      TradeItem(
        id: '2',
        tokenName: 'Pepe',
        tokenSymbol: 'PEPE',
        tokenImage: 'assets/images/pepe.png',
        type: TradeType.sell,
        amount: 1.2,
        price: 0.00000089,
        currentPrice: 0.00000082,
        profitLoss: -0.15,
        profitLossPercentage: -7.9,
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        status: TradeStatus.live,
        isActive: true,
      ),
      TradeItem(
        id: '3',
        tokenName: 'Dogwifhat',
        tokenSymbol: 'WIF',
        tokenImage: 'assets/images/wif.png',
        type: TradeType.buy,
        amount: 0.3,
        price: 2.45,
        currentPrice: 3.02,
        profitLoss: 0.171,
        profitLossPercentage: 23.3,
        timestamp: DateTime.now().subtract(const Duration(hours: 6)),
        status: TradeStatus.live,
        isActive: true,
      ),

      // Watching trades
      TradeItem(
        id: '4',
        tokenName: 'Solana',
        tokenSymbol: 'SOL',
        tokenImage: 'assets/images/sol.png',
        type: TradeType.buy,
        amount: 2.0,
        price: 98.50,
        currentPrice: 102.30,
        profitLoss: 7.60,
        profitLossPercentage: 3.9,
        timestamp: DateTime.now().subtract(const Duration(days: 1)),
        status: TradeStatus.watching,
        isActive: false,
      ),
      TradeItem(
        id: '5',
        tokenName: 'Jupiter',
        tokenSymbol: 'JUP',
        tokenImage: 'assets/images/jup.png',
        type: TradeType.sell,
        amount: 50.0,
        price: 0.85,
        currentPrice: 0.78,
        profitLoss: -3.50,
        profitLossPercentage: -8.2,
        timestamp: DateTime.now().subtract(const Duration(days: 2)),
        status: TradeStatus.watching,
        isActive: false,
      ),

      // Archive trades
      TradeItem(
        id: '6',
        tokenName: 'Raydium',
        tokenSymbol: 'RAY',
        tokenImage: 'assets/images/ray.png',
        type: TradeType.buy,
        amount: 5.0,
        price: 1.20,
        currentPrice: 1.45,
        profitLoss: 1.25,
        profitLossPercentage: 20.8,
        timestamp: DateTime.now().subtract(const Duration(days: 7)),
        status: TradeStatus.archive,
        isActive: false,
      ),
      TradeItem(
        id: '7',
        tokenName: 'Orca',
        tokenSymbol: 'ORCA',
        tokenImage: 'assets/images/orca.png',
        type: TradeType.sell,
        amount: 10.0,
        price: 3.80,
        currentPrice: 3.45,
        profitLoss: -3.50,
        profitLossPercentage: -9.2,
        timestamp: DateTime.now().subtract(const Duration(days: 14)),
        status: TradeStatus.archive,
        isActive: false,
      ),
    ];
  }

  void _filterTrades() {
    setState(() {
      switch (_selectedCategory) {
        case 'All':
          _filteredTrades = _allTrades;
          break;
        case 'Live':
          _filteredTrades = _allTrades
              .where((trade) => trade.status == TradeStatus.live)
              .toList();
          break;
        case 'Watching':
          _filteredTrades = _allTrades
              .where((trade) => trade.status == TradeStatus.watching)
              .toList();
          break;
        case 'Archive':
          _filteredTrades = _allTrades
              .where((trade) => trade.status == TradeStatus.archive)
              .toList();
          break;
      }
    });
  }

  void _startPriceUpdates() {
    _priceUpdateTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (mounted) {
        setState(() {
          for (var trade in _allTrades) {
            if (trade.status == TradeStatus.live) {
              // Simulate price changes (-2% to +2%)
              double changePercent = (_random.nextDouble() - 0.5) * 0.04;
              trade = TradeItem(
                id: trade.id,
                tokenName: trade.tokenName,
                tokenSymbol: trade.tokenSymbol,
                tokenImage: trade.tokenImage,
                type: trade.type,
                amount: trade.amount,
                price: trade.price,
                currentPrice: trade.currentPrice * (1 + changePercent),
                profitLoss:
                    (trade.currentPrice * (1 + changePercent) - trade.price) *
                    trade.amount,
                profitLossPercentage:
                    ((trade.currentPrice * (1 + changePercent) - trade.price) /
                        trade.price) *
                    100,
                timestamp: trade.timestamp,
                status: trade.status,
                isActive: trade.isActive,
              );

              // Update the trade in the list
              int index = _allTrades.indexWhere((t) => t.id == trade.id);
              if (index != -1) {
                _allTrades[index] = trade;
              }
            }
          }
        });
        _filterTrades();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        title: Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('TRADES ', style: TextStyle(fontWeight: FontWeight.bold)),
                // Text(
                //   '${_filteredTrades.length} trades',
                //   style: TextStyle(
                //     color: Colors.white.withValues(alpha: 0.7),
                //     fontSize: 14,
                //   ),
                // ),
              ],
            ),
            Spacer(),
            GestureDetector(
              onTap: _showCategoryModal,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: _getCategoryColor().withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: _getCategoryColor().withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: _getCategoryColor(),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _selectedCategory.toUpperCase(),
                      style: TextStyle(
                        color: _getCategoryColor(),
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      LucideIcons.chevronDown,
                      color: _getCategoryColor(),
                      size: 16,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        centerTitle: false,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Trade list
          Expanded(
            child: _filteredTrades.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _filteredTrades.length,
                    itemBuilder: (context, index) {
                      final trade = _filteredTrades[index];
                      return _buildTradeCard(trade);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildTradeCard(TradeItem trade) {
    final isProfit = trade.profitLoss > 0;
    final profitColor = isProfit ? Colors.green : Colors.red;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.08)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                // Token icon
                Container(
                  width: 44,
                  height: 44,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.purple.withValues(alpha: 0.8),
                        Colors.blue.withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(22),
                  ),
                  child: Center(
                    child: Text(
                      trade.tokenSymbol.substring(0, 2),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                // Token info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            trade.tokenName,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: trade.type == TradeType.buy
                                  ? Colors.green.withValues(alpha: 0.2)
                                  : Colors.red.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              trade.type == TradeType.buy ? 'BUY' : 'SELL',
                              style: TextStyle(
                                color: trade.type == TradeType.buy
                                    ? Colors.green
                                    : Colors.red,
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${trade.amount.toStringAsFixed(2)} SOL',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),

                // P&L info
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${isProfit ? '+' : ''}${trade.profitLossPercentage.toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: profitColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${isProfit ? '+' : ''}${trade.profitLoss.toStringAsFixed(3)} SOL',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Price info and action buttons
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Entry: \$${trade.price.toStringAsFixed(trade.price < 0.01 ? 8 : 4)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withValues(alpha: 0.6),
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'Current: \$${trade.currentPrice.toStringAsFixed(trade.currentPrice < 0.01 ? 8 : 4)}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

                // Action buttons based on trade status
                if (trade.status == TradeStatus.live) ...[
                  ElevatedButton(
                    onPressed: () => _showSellConfirmation(trade),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.withValues(alpha: 0.2),
                      foregroundColor: Colors.red,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(
                          color: Colors.red.withValues(alpha: 0.3),
                          width: 0.5,
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(LucideIcons.trendingDown, size: 14),
                        const SizedBox(width: 4),
                        const Text(
                          'Manual Sell',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ] else if (trade.status == TradeStatus.watching) ...[
                  ElevatedButton(
                    onPressed: () => _showBuyConfirmation(trade),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.withValues(alpha: 0.2),
                      foregroundColor: Colors.green,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(
                          color: Colors.green.withValues(alpha: 0.3),
                          width: 0.5,
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(LucideIcons.trendingUp, size: 14),
                        const SizedBox(width: 4),
                        const Text(
                          'Manual Buy',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ] else ...[
                  // Archive trades show status badge
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(
                        trade.status,
                      ).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _getStatusColor(
                          trade.status,
                        ).withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      _getStatusText(trade.status),
                      style: TextStyle(
                        color: _getStatusColor(trade.status),
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            LucideIcons.activity,
            color: Colors.white.withValues(alpha: 0.3),
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'No ${_selectedCategory.toLowerCase()} trades',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your ${_selectedCategory.toLowerCase()} trades will appear here',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.5),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor() {
    switch (_selectedCategory) {
      case 'Live':
        return Colors.green;
      case 'Watching':
        return Colors.orange;
      case 'Archive':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  Color _getStatusColor(TradeStatus status) {
    switch (status) {
      case TradeStatus.live:
        return Colors.green;
      case TradeStatus.watching:
        return Colors.orange;
      case TradeStatus.archive:
        return Colors.grey;
    }
  }

  String _getStatusText(TradeStatus status) {
    switch (status) {
      case TradeStatus.live:
        return 'LIVE';
      case TradeStatus.watching:
        return 'WATCHING';
      case TradeStatus.archive:
        return 'ARCHIVED';
    }
  }

  void _showCategoryModal() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        decoration: const BoxDecoration(
          color: Color(0xFF1A1A1A),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(bottom: 20),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.white24,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            const Text(
              'Select Trade Category',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 24),

            // Category options
            ...[
              'All',
              'Live',
              'Watching',
              'Archive',
            ].map((category) => _buildCategoryOption(category)).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryOption(String category) {
    final isSelected = _selectedCategory == category;
    final color = _getCategoryColorForCategory(category);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedCategory = category;
          });
          _filterTrades();
          Navigator.pop(context);
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected
                ? color.withValues(alpha: 0.2)
                : Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected
                  ? color.withValues(alpha: 0.3)
                  : Colors.white.withValues(alpha: 0.1),
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(_getCategoryIcon(category), color: color, size: 16),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      category,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      _getCategoryDescription(category),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected) Icon(LucideIcons.check, color: color, size: 20),
            ],
          ),
        ),
      ),
    );
  }

  Color _getCategoryColorForCategory(String category) {
    switch (category) {
      case 'Live':
        return Colors.green;
      case 'Watching':
        return Colors.orange;
      case 'Archive':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'All':
        return LucideIcons.list;
      case 'Live':
        return LucideIcons.activity;
      case 'Watching':
        return LucideIcons.eye;
      case 'Archive':
        return LucideIcons.archive;
      default:
        return LucideIcons.list;
    }
  }

  String _getCategoryDescription(String category) {
    switch (category) {
      case 'All':
        return 'View all trades across categories';
      case 'Live':
        return 'Active trades with real-time updates';
      case 'Watching':
        return 'Trades you\'re monitoring';
      case 'Archive':
        return 'Completed and closed trades';
      default:
        return '';
    }
  }

  void _showSellConfirmation(TradeItem trade) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1A1A1A),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(LucideIcons.trendingDown, color: Colors.red, size: 24),
            const SizedBox(width: 12),
            const Text(
              'Sell Trade',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to manually sell your ${trade.tokenName} position?',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Amount:',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        '${trade.amount.toStringAsFixed(2)} SOL',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Current Price:',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        '\$${trade.currentPrice.toStringAsFixed(trade.currentPrice < 0.01 ? 8 : 4)}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Est. P&L:',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        '${trade.profitLoss > 0 ? '+' : ''}${trade.profitLoss.toStringAsFixed(3)} SOL',
                        style: TextStyle(
                          color: trade.profitLoss > 0
                              ? Colors.green
                              : Colors.red,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 16,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _executeSell(trade);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Confirm Sell',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  void _showBuyConfirmation(TradeItem trade) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1A1A1A),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(LucideIcons.trendingUp, color: Colors.green, size: 24),
            const SizedBox(width: 12),
            const Text(
              'Buy Trade',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to manually buy ${trade.tokenName}?',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Amount:',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        '${trade.amount.toStringAsFixed(2)} SOL',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Current Price:',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        '\$${trade.currentPrice.toStringAsFixed(trade.currentPrice < 0.01 ? 8 : 4)}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Est. Cost:',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        '${(trade.currentPrice * trade.amount).toStringAsFixed(3)} SOL',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 16,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _executeBuy(trade);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Confirm Buy',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  void _executeSell(TradeItem trade) {
    // Move trade to archive and show success message
    setState(() {
      int index = _allTrades.indexWhere((t) => t.id == trade.id);
      if (index != -1) {
        _allTrades[index] = TradeItem(
          id: trade.id,
          tokenName: trade.tokenName,
          tokenSymbol: trade.tokenSymbol,
          tokenImage: trade.tokenImage,
          type: trade.type,
          amount: trade.amount,
          price: trade.price,
          currentPrice: trade.currentPrice,
          profitLoss: trade.profitLoss,
          profitLossPercentage: trade.profitLossPercentage,
          timestamp: trade.timestamp,
          status: TradeStatus.archive,
          isActive: false,
        );
      }
    });
    _filterTrades();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${trade.tokenName} sold successfully!'),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _executeBuy(TradeItem trade) {
    // Move trade to live and show success message
    setState(() {
      int index = _allTrades.indexWhere((t) => t.id == trade.id);
      if (index != -1) {
        _allTrades[index] = TradeItem(
          id: trade.id,
          tokenName: trade.tokenName,
          tokenSymbol: trade.tokenSymbol,
          tokenImage: trade.tokenImage,
          type: TradeType.buy,
          amount: trade.amount,
          price: trade.currentPrice, // Use current price as new entry price
          currentPrice: trade.currentPrice,
          profitLoss: 0.0, // Reset P&L
          profitLossPercentage: 0.0,
          timestamp: DateTime.now(), // Update timestamp
          status: TradeStatus.live,
          isActive: true,
        );
      }
    });
    _filterTrades();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${trade.tokenName} bought successfully!'),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
