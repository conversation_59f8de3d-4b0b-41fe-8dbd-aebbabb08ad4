import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../models/coin_model.dart';
import '../widgets/gradient_button.dart';
import '../widgets/coin_chart.dart';
import '../widgets/ai_prediction.dart';
import '../widgets/bot_config_modal.dart';
import '../responsive.dart';

class ResponsiveCoinDetailPage extends StatefulWidget {
  final String symbol;
  final CoinData coin;

  const ResponsiveCoinDetailPage({
    super.key,
    required this.symbol,
    required this.coin,
  });

  @override
  State<ResponsiveCoinDetailPage> createState() => _ResponsiveCoinDetailPageState();
}

class _ResponsiveCoinDetailPageState extends State<ResponsiveCoinDetailPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0F0F0F),
      appBar: _buildAppBar(),
      body: ResponsiveHelper.isDesktop(context) 
          ? _buildDesktopLayout() 
          : _buildMobileLayout(),
      bottomNavigationBar: ResponsiveHelper.isDesktop(context) 
          ? null 
          : _buildBottomButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    final isPositive = widget.coin.priceChangePercentage24h != null &&
        widget.coin.priceChangePercentage24h! >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;

    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(LucideIcons.arrowLeft, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      title: Row(
        children: [
          // Coin Icon
          widget.coin.image != null
              ? Image.network(
                  widget.coin.image!,
                  width: 36,
                  height: 36,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => const Icon(
                    Icons.currency_bitcoin,
                    color: Colors.white,
                  ),
                )
              : Center(
                  child: Text(
                    widget.coin.symbol.substring(0, 1).toUpperCase(),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
          const SizedBox(width: 12),
          // Coin Name and Symbol
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.coin.displayName,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 17,
                    color: Colors.white,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  widget.coin.displaySymbol,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          // Price and Change
          if (!ResponsiveHelper.isDesktop(context))
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  widget.coin.formattedPrice,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 15,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      isPositive ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                      color: changeColor,
                      size: 16,
                    ),
                    Text(
                      widget.coin.formattedPriceChange,
                      style: TextStyle(
                        color: changeColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left Column - Chart
          Expanded(
            flex: 3,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.03),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.08),
                  width: 1,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPriceHeader(),
                    const SizedBox(height: 24),
                    Expanded(
                      child: CoinChart(
                        symbol: widget.coin.symbol,
                        currentPrice: widget.coin.currentPrice ?? 0.0,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 24),
          
          // Right Column - Info, AI Prediction, Stats, Action
          Expanded(
            flex: 2,
            child: Column(
              children: [
                // AI Prediction
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.03),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.08),
                      width: 1,
                    ),
                  ),
                  child: AIPrediction(
                    symbol: widget.coin.symbol,
                    currentPrice: widget.coin.currentPrice ?? 0.0,
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Stats section
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.03),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.08),
                      width: 1,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Market Statistics',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildStatRow('Market Cap', widget.coin.formattedMarketCap),
                        const SizedBox(height: 16),
                        _buildStatRow('24h Volume', widget.coin.formattedVolume),
                        const SizedBox(height: 16),
                        _buildStatRow(
                          'Rank',
                          widget.coin.marketCapRank != null
                              ? '#${widget.coin.marketCapRank}'
                              : 'N/A',
                        ),
                        const SizedBox(height: 16),
                        _buildStatRow(
                          'Supply',
                          widget.coin.circulatingSupply != null
                              ? '${(widget.coin.circulatingSupply! / 1000000).toStringAsFixed(1)}M'
                              : 'N/A',
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Action Button
                SizedBox(
                  width: double.infinity,
                  child: GradientButton(
                    text: 'Start Trading Bot',
                    onPressed: _showCreateBotModal,
                    icon: LucideIcons.bot,
                    height: 50,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Real-time Chart
          CoinChart(
            symbol: widget.coin.symbol,
            currentPrice: widget.coin.currentPrice ?? 0.0,
          ),

          // AI Prediction
          AIPrediction(
            symbol: widget.coin.symbol,
            currentPrice: widget.coin.currentPrice ?? 0.0,
          ),

          const SizedBox(height: 24),

          // Stats section
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                _buildStatRow('Market Cap', widget.coin.formattedMarketCap),
                const SizedBox(height: 16),
                _buildStatRow('24h Volume', widget.coin.formattedVolume),
                const SizedBox(height: 16),
                _buildStatRow(
                  'Rank',
                  widget.coin.marketCapRank != null
                      ? '#${widget.coin.marketCapRank}'
                      : 'N/A',
                ),
                const SizedBox(height: 16),
                _buildStatRow(
                  'Supply',
                  widget.coin.circulatingSupply != null
                      ? '${(widget.coin.circulatingSupply! / 1000000).toStringAsFixed(1)}M'
                      : 'N/A',
                ),
              ],
            ),
          ),
          // Bottom padding for safe area
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildPriceHeader() {
    final isPositive = widget.coin.priceChangePercentage24h != null &&
        widget.coin.priceChangePercentage24h! >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.coin.formattedPrice,
          style: const TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Icon(
              isPositive ? Icons.arrow_drop_up : Icons.arrow_drop_down,
              color: changeColor,
              size: 20,
            ),
            Text(
              widget.coin.formattedPriceChange,
              style: TextStyle(
                color: changeColor,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '(24h)',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            color: Colors.white.withValues(alpha: 0.6),
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomButton() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: GradientButton(
          text: 'Start Trading',
          onPressed: _showCreateBotModal,
          icon: LucideIcons.bot,
        ),
      ),
    );
  }

  void _showCreateBotModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreateBotModal(
        coinSymbol: widget.symbol,
        onBotCreated: () {
          // Bot created callback
        },
      ),
    );
  }
}
