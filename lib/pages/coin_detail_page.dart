import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../models/coin_model.dart';
import '../widgets/gradient_button.dart';
import '../widgets/coin_chart.dart';
import '../widgets/ai_prediction.dart';
import '../widgets/bot_config_modal.dart';

class CoinDetailPage extends StatefulWidget {
  final String symbol;
  final CoinData coin;

  const CoinDetailPage({super.key, required this.symbol, required this.coin});

  @override
  State<CoinDetailPage> createState() => _CoinDetailPageState();
}

class _CoinDetailPageState extends State<CoinDetailPage> {
  @override
  Widget build(BuildContext context) {
    final isPositive =
        widget.coin.priceChangePercentage24h != null &&
        widget.coin.priceChangePercentage24h! >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;

    return Scaffold(
      backgroundColor: const Color(0xFF0F0F0F),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(LucideIcons.arrowLeft, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Row(
          children: [
            // Coin Icon and Rank
            widget.coin.image != null
                ? Image.network(
                    widget.coin.image!,
                    width: 36,
                    height: 36,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Icon(
                      Icons.currency_bitcoin,
                      // color: textColor.withValues(alpha: 0.7),
                    ),
                  )
                : Center(
                    child: Text(
                      widget.coin.symbol.substring(0, 1).toUpperCase(),
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
            SizedBox(width: 12),
            // Coin Name and Symbol
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.coin.displayName,
                    style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    widget.coin.displaySymbol,
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                  ),
                ],
              ),
            ),

            // Price and Change
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  widget.coin.formattedPrice,
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      isPositive ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                      color: changeColor,
                      size: 16,
                    ),
                    Text(
                      widget.coin.formattedPriceChange,
                      style: TextStyle(
                        color: changeColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Real-time Chart
            CoinChart(
              symbol: widget.coin.symbol,
              currentPrice: widget.coin.currentPrice ?? 0.0,
            ),

            // AI Prediction
            AIPrediction(
              symbol: widget.coin.symbol,
              currentPrice: widget.coin.currentPrice ?? 0.0,
            ),

            const SizedBox(height: 24),

            // Stats section
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  _buildStatRow('Market Cap', widget.coin.formattedMarketCap),
                  const SizedBox(height: 16),
                  _buildStatRow('24h Volume', widget.coin.formattedVolume),
                  const SizedBox(height: 16),
                  _buildStatRow(
                    'Rank',
                    widget.coin.marketCapRank != null
                        ? '#${widget.coin.marketCapRank}'
                        : 'N/A',
                  ),
                  const SizedBox(height: 16),
                  _buildStatRow(
                    'Supply',
                    widget.coin.circulatingSupply != null
                        ? '${(widget.coin.circulatingSupply! / 1000000).toStringAsFixed(1)}M'
                        : 'N/A',
                  ),
                ],
              ),
            ),
            // Bottom padding for safe area
            const SizedBox(height: 32),
          ],
        ),
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: GradientButton(
            text: 'Start Trading',
            onPressed: _showCreateBotModal,
            icon: LucideIcons.bot,
          ),
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            color: Colors.white.withValues(alpha: 0.6),
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  void _showCreateBotModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreateBotModal(
        coinSymbol: widget.symbol,
        onBotCreated: () {
          // Bot created callback
        },
      ),
    );
  }
}
