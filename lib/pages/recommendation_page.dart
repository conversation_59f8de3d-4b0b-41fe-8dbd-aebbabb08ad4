import 'package:dextrip_app/widgets/bot_config_modal.dart';
import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'dart:async';
import '../widgets/swipeable_card_stack.dart';
import '../widgets/agent_selector_modal.dart';
import '../widgets/trading_recommendation_card.dart';
import '../models/coin_model.dart';
import '../models/trading_bot_model.dart';
import '../theme/app_theme.dart';

class Recommendation {
  final String id;
  final String agentName;
  final String agentAvatarUrl;
  final CoinData coin;
  final String direction; // Long or Short
  final String description;
  final String timeAgo;
  final double targetPrice;
  final double stopLoss;
  final List<double> chartData;
  final double takeProfit;
  final double stopLossPercent;
  final String confidence;

  Recommendation({
    required this.id,
    required this.agentName,
    required this.agentAvatarUrl,
    required this.coin,
    required this.direction,
    required this.description,
    required this.timeAgo,
    required this.targetPrice,
    required this.stopLoss,
    required this.chartData,
    required this.takeProfit,
    required this.stopLossPercent,
    required this.confidence,
  });
}

class RecommendationPage extends StatefulWidget {
  const RecommendationPage({super.key});

  @override
  State<RecommendationPage> createState() => _RecommendationPageState();
}

class _RecommendationPageState extends State<RecommendationPage> {
  late List<Recommendation> _recommendations;
  final GlobalKey<SwipeableCardStackState> _cardStackKey = GlobalKey();
  String _selectedAgentId = 'donalt';
  String _selectedAgentName = 'DonAlt';

  // Countdown timer state
  Timer? _countdownTimer;
  int _countdownSeconds = 10;
  bool _isTradeButtonActive = false;
  bool _showTradeOutline = false;
  bool _isSkipButtonActive = false;
  bool _showSkipOutline = false;

  // Card swipe progress state
  double _swipeProgress = 0.0;
  bool _isCardSwipingRight = false;
  bool _isCardBeingDragged = false;

  @override
  void initState() {
    super.initState();
    _recommendations = _generateDummyData();
    _startCountdown();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    _countdownSeconds = 10;
    _isTradeButtonActive = false;
    _showTradeOutline = false;
    _isSkipButtonActive = false;
    _showSkipOutline = false;
    _swipeProgress = 0.0;
    _isCardSwipingRight = false;

    _countdownTimer?.cancel();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        // Only countdown if card is not being dragged
        if (!_isCardBeingDragged && _countdownSeconds > 0) {
          _countdownSeconds--;
        } else if (!_isCardBeingDragged && _countdownSeconds <= 0) {
          // Auto-advance to next card
          _cardStackKey.currentState?.swipeLeft();
          _startCountdown(); // Restart countdown for next card
        }
      });
    });
  }

  void _onSwipeProgress(double progress, bool isRight) {
    setState(() {
      _swipeProgress = progress;
      _isCardSwipingRight = isRight;
    });
  }

  void _onDragStateChanged(bool isDragging) {
    setState(() {
      _isCardBeingDragged = isDragging;
    });
  }

  void _onTradeButtonPressed() {
    setState(() {
      _isTradeButtonActive = true;
      _showTradeOutline = true;
      // Trigger card swipe animation immediately
      _swipeProgress = 0.8;
      _isCardSwipingRight = true;
    });

    // Show outline for 1 second then execute trade
    Timer(const Duration(seconds: 1), () {
      _cardStackKey.currentState?.swipeRight();
    });
  }

  void _onSkipButtonPressed() {
    setState(() {
      _isSkipButtonActive = true;
      _showSkipOutline = true;
      // Trigger card swipe animation immediately
      _swipeProgress = 0.8;
      _isCardSwipingRight = false;
    });

    // Show outline for 1 second then execute skip
    Timer(const Duration(seconds: 1), () {
      _cardStackKey.currentState?.swipeLeft();
    });
  }

  void _onSwipeRight(int index) {
    final rec = _recommendations[index];
    _startTrade(rec);
    _startCountdown(); // Restart countdown for next card
  }

  void _onSwipeLeft(int index) {
    final rec = _recommendations[index];
    _skipRecommendation(rec);
    _startCountdown(); // Restart countdown for next card
  }

  void _startTrade(Recommendation rec) {
    // TODO: Navigate to trade setup page
  }

  void _skipRecommendation(Recommendation rec) {
    // Just skip silently
  }

  void _changeAgent() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => AgentSelectorModal(
        currentAgentId: _selectedAgentId,
        onAgentSelected: (agent) {
          setState(() {
            _selectedAgentId = agent.id;
            _selectedAgentName = agent.name;
          });
          // TODO: Refresh recommendations for new agent
        },
      ),
    );
  }

  void _openIndicators() {
    // Create a dummy bot for configuration
    final dummyBot = TradingBot(
      id: 'recommendation_bot',
      name: 'Recommendation Bot',
      tokenSymbol: 'BTC',
      tokenImage: '',
      currentPrice: 0.0,
      priceChange24h: 0.0,
      isActive: false,
      strategy: 'DCA',
      solPerTrade: 0.1,
      totalTrades: 0,
      winRate: 0.0,
      totalEarned: 0.0,
      trades: [],
    );

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => BotConfigModal(bot: dummyBot),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        elevation: 0,
        title: Row(
          children: [
            // Agent selector
            GestureDetector(
              onTap: _changeAgent,
              child: Row(
                children: [
                  const CircleAvatar(
                    backgroundImage: AssetImage('assets/avatars/agent.png'),
                    radius: 16,
                  ),
                  const SizedBox(width: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Text(
                      //   'AI Agent',
                      //   style: TextStyle(color: Colors.grey[400], fontSize: 12),
                      // ),
                      Row(
                        children: [
                          Text(
                            _selectedAgentName,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              fontSize: 22,
                            ),
                          ),
                          const SizedBox(width: 4),
                          const Icon(
                            LucideIcons.chevronDown,
                            size: 16,
                            color: Colors.white,
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const Spacer(),
            // Indicator settings
            IconButton(
              icon: const Icon(LucideIcons.bolt, color: Colors.white, size: 20),
              onPressed: _openIndicators,
            ),
          ],
        ),
      ),
      body: SafeArea(
        child: _recommendations.isEmpty
            ? const Center(
                child: Text(
                  'No recommendations available',
                  style: TextStyle(color: Colors.white, fontSize: 18),
                ),
              )
            : Column(
                children: [
                  // Card stack
                  Expanded(
                    child: SwipeableCardStack(
                      key: _cardStackKey,
                      cards: _recommendations
                          .map(
                            (rec) => SwipeableRecommendationCard(
                              recommendation: rec,
                              swipeProgress: _swipeProgress,
                              isSwipingRight: _isCardSwipingRight,
                            ),
                          )
                          .toList(),
                      onSwipe: (index, direction) {
                        if (direction == SwipeDirection.right) {
                          _onSwipeRight(index);
                        } else {
                          _onSwipeLeft(index);
                        }
                      },
                      onSwipeProgress: _onSwipeProgress,
                      onDragStateChange: _onDragStateChanged,
                      cardHeight: MediaQuery.of(context).size.height * 0.6,
                    ),
                  ),

                  // Fixed buttons at bottom
                  Container(
                    padding: const EdgeInsets.all(20),
                    child: Row(
                      children: [
                        Expanded(child: _buildSkipButton()),
                        const SizedBox(width: 16),
                        Expanded(child: _buildTradeButton()),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildSkipButton() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        gradient: _isSkipButtonActive
            ? const LinearGradient(
                colors: [Color(0xFFFF4444), Color(0xFFCC0000)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
        borderRadius: BorderRadius.circular(12),
        border: _showSkipOutline
            ? Border.all(color: Colors.red, width: 2)
            : Border.all(color: Colors.grey[400]!, width: 1),
      ),
      child: ElevatedButton(
        onPressed: _onSkipButtonPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Skip (${_countdownSeconds}s)',
              style: TextStyle(
                color: _isSkipButtonActive
                    ? Colors.white
                    : Theme.of(context).textTheme.bodyMedium?.color,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              LucideIcons.x,
              color: _isSkipButtonActive
                  ? Colors.white
                  : Theme.of(context).textTheme.bodyMedium?.color,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTradeButton() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        gradient: _isTradeButtonActive ? AppTheme.buttonGradient : null,
        borderRadius: BorderRadius.circular(12),
        border: _showTradeOutline
            ? Border.all(color: AppTheme.primaryColor, width: 2)
            : Border.all(color: Colors.grey[400]!, width: 1),
      ),
      child: ElevatedButton(
        onPressed: _onTradeButtonPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Trade',
              style: TextStyle(
                color: _isTradeButtonActive
                    ? Colors.white
                    : Theme.of(context).textTheme.bodyMedium?.color,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              LucideIcons.arrowRight,
              color: _isTradeButtonActive
                  ? Colors.white
                  : Theme.of(context).textTheme.bodyMedium?.color,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  List<Recommendation> _generateDummyData() {
    return [
      Recommendation(
        id: '1',
        agentName: 'DonAlt',
        agentAvatarUrl: 'assets/avatars/agent.png',
        coin: CoinData(
          id: 'ethereum',
          symbol: 'ETH',
          name: 'Ethereum',
          image:
              'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
          currentPrice: 3693.22,
          marketCap: 444000000000,
          marketCapRank: 2,
          priceChangePercentage24h: 2.5,
          totalVolume: 15000000000,
        ),
        direction: 'Long',
        description:
            'ETHUSD showing strength! Moderate bullish momentum means it\'s time to go long with caution. Target Price: \$3780, Stop Loss: \$3540.',
        timeAgo: '16 minutes ago',
        targetPrice: 3780.0,
        stopLoss: 3540.0,
        chartData: [0.2, 0.4, 0.3, 0.7, 0.5, 0.8, 0.6, 0.9, 0.7, 0.8],
        takeProfit: 15.0,
        stopLossPercent: -17.0,
        confidence: 'High',
      ),
      Recommendation(
        id: '2',
        agentName: 'DonAlt',
        agentAvatarUrl: 'assets/avatars/agent.png',
        coin: CoinData(
          id: 'bitcoin',
          symbol: 'BTC',
          name: 'Bitcoin',
          image:
              'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
          currentPrice: 62150.0,
          marketCap: 1230000000000,
          marketCapRank: 1,
          priceChangePercentage24h: -1.8,
          totalVolume: 28000000000,
        ),
        direction: 'Short',
        description:
            'Bitcoin showing weakness at resistance. Technical indicators suggest a pullback is imminent. Consider shorting with tight risk management.',
        timeAgo: '32 minutes ago',
        targetPrice: 60000.0,
        stopLoss: 64000.0,
        chartData: [0.8, 0.7, 0.6, 0.4, 0.5, 0.3, 0.4, 0.2, 0.3, 0.1],
        takeProfit: -12.0,
        stopLossPercent: 8.0,
        confidence: 'Medium',
      ),
      Recommendation(
        id: '3',
        agentName: 'DonAlt',
        agentAvatarUrl: 'assets/avatars/agent.png',
        coin: CoinData(
          id: 'solana',
          symbol: 'SOL',
          name: 'Solana',
          image:
              'https://assets.coingecko.com/coins/images/4128/large/solana.png',
          currentPrice: 162.50,
          marketCap: 76000000000,
          marketCapRank: 5,
          priceChangePercentage24h: 4.2,
          totalVolume: 3200000000,
        ),
        direction: 'Long',
        description:
            'Solana breaking out of consolidation pattern. Strong volume confirms the move. Perfect setup for a swing trade.',
        timeAgo: '1 hour ago',
        targetPrice: 180.0,
        stopLoss: 145.0,
        chartData: [0.3, 0.2, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.85, 0.9],
        takeProfit: 22.0,
        stopLossPercent: -15.0,
        confidence: 'High',
      ),
    ];
  }
}
