import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../models/coin_model.dart';
import '../models/trading_bot.dart';
import '../services/dexscreener_service.dart';
import '../widgets/gradient_button.dart';
import '../theme/app_theme.dart';

class BotConfigPage extends StatefulWidget {
  const BotConfigPage({super.key});

  @override
  State<BotConfigPage> createState() => _BotConfigPageState();
}

class IndicatorConfig {
  final String name;
  final Map<String, dynamic> parameters;
  final double buyThreshold;
  final double sellThreshold;

  IndicatorConfig({
    required this.name,
    required this.parameters,
    required this.buyThreshold,
    required this.sellThreshold,
  });

  IndicatorConfig copyWith({
    Map<String, dynamic>? parameters,
    double? buyThreshold,
    double? sellThreshold,
  }) {
    return IndicatorConfig(
      name: name,
      parameters: parameters ?? this.parameters,
      buyThreshold: buyThreshold ?? this.buyThreshold,
      sellThreshold: sellThreshold ?? this.sellThreshold,
    );
  }
}

class _BotConfigPageState extends State<BotConfigPage> {
  final PageController _pageController = PageController();
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _investmentController = TextEditingController();
  final _stopLossController = TextEditingController();
  final _takeProfitController = TextEditingController();
  final _searchController = TextEditingController();

  int _currentStep = 0;
  CoinData? _selectedCoin;
  List<CoinData> _searchResults = [];
  List<CoinData> _trendingCoins = [];
  bool _isLoadingTrending = true;
  String _trendingError = '';

  final Set<String> _selectedIndicators = {};
  final Map<String, IndicatorConfig> _indicatorConfigs = {};

  @override
  void initState() {
    super.initState();
    _loadTrendingCoins();
  }

  Future<void> _loadTrendingCoins() async {
    setState(() {
      _isLoadingTrending = true;
      _trendingError = '';
    });

    try {
      final coins = await CoinGeckoService.getTrendingCoins();
      setState(() {
        _trendingCoins = coins.take(10).toList(); // Limit to 10 coins
        _isLoadingTrending = false;
      });
    } catch (e) {
      setState(() {
        _trendingError = 'Failed to load trending coins. Please try again.';
        _isLoadingTrending = false;
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _nameController.dispose();
    _investmentController.dispose();
    _stopLossController.dispose();
    _takeProfitController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep == 0 && _selectedCoin == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please select a coin')));
      return;
    }

    if (_currentStep == 1) {
      if (_selectedIndicators.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please select at least one indicator')),
        );
        return;
      }

      // Initialize indicator configurations if not already done
      for (final indicator in _selectedIndicators) {
        if (!_indicatorConfigs.containsKey(indicator)) {
          final type = IndicatorType.values.firstWhere(
            (t) => t.name == indicator,
          );
          _indicatorConfigs[indicator] = IndicatorConfig(
            name: indicator,
            parameters: _getDefaultParameters(type),
            buyThreshold: _getDefaultBuyThreshold(type),
            sellThreshold: _getDefaultSellThreshold(type),
          );
        }
      }
    }

    if (_currentStep < 2) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      setState(() => _currentStep++);
    } else if (_formKey.currentState!.validate()) {
      _createBot();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() => _currentStep--);
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _searchCoins(String query) async {
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
      });
      return;
    }

    setState(() {});

    try {
      final results = await CoinGeckoService.searchCoins(query);
      setState(() {
        _searchResults = results.take(10).toList();
      });
    } catch (e) {
      setState(() {
        _searchResults = [];
      });
    }
  }

  Map<String, dynamic> _getDefaultParameters(IndicatorType type) {
    switch (type) {
      case IndicatorType.rsi:
        return {'period': 14};
      case IndicatorType.macd:
        return {'fastPeriod': 12, 'slowPeriod': 26, 'signalPeriod': 9};
      case IndicatorType.sma:
        return {'period': 20};
      case IndicatorType.ema:
        return {'period': 20};
      case IndicatorType.bollinger:
        return {'period': 20, 'standardDeviations': 2};
      case IndicatorType.stochastic:
        return {'kPeriod': 14, 'dPeriod': 3};
      case IndicatorType.williams:
        return {'period': 14};
      case IndicatorType.adx:
        return {'period': 14};
    }
  }

  double _getDefaultBuyThreshold(IndicatorType type) {
    switch (type) {
      case IndicatorType.rsi:
        return 30.0;
      case IndicatorType.macd:
        return 0.0;
      case IndicatorType.stochastic:
        return 20.0;
      case IndicatorType.williams:
        return -80.0;
      default:
        return 0.0;
    }
  }

  double _getDefaultSellThreshold(IndicatorType type) {
    switch (type) {
      case IndicatorType.rsi:
        return 70.0;
      case IndicatorType.macd:
        return 0.0;
      case IndicatorType.stochastic:
        return 80.0;
      case IndicatorType.williams:
        return -20.0;
      default:
        return 0.0;
    }
  }

  void _createBot() {
    if (_selectedCoin == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please select a coin')));
      return;
    }

    if (_selectedIndicators.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one indicator')),
      );
      return;
    }

    if (!_formKey.currentState!.validate()) {
      return;
    }

    final bot = TradingBot(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: _nameController.text,
      coin: _selectedCoin!,
      indicators: _selectedIndicators
          .map(
            (indicator) => TradingIndicator(
              type: IndicatorType.values.firstWhere(
                (type) => type.name == indicator,
              ),
              parameters: _getDefaultParameters(
                IndicatorType.values.firstWhere(
                  (type) => type.name == indicator,
                ),
              ),
              buyThreshold: _getDefaultBuyThreshold(
                IndicatorType.values.firstWhere(
                  (type) => type.name == indicator,
                ),
              ),
              sellThreshold: _getDefaultSellThreshold(
                IndicatorType.values.firstWhere(
                  (type) => type.name == indicator,
                ),
              ),
            ),
          )
          .toList(),
      investmentAmount: double.parse(_investmentController.text),
      stopLoss: double.parse(_stopLossController.text),
      takeProfit: double.parse(_takeProfitController.text),
      createdAt: DateTime.now(),
    );

    Navigator.pop(context, bot);
  }

  String _getStepTitle() {
    switch (_currentStep) {
      case 0:
        return 'SELECT COIN';
      case 1:
        return 'ADD INDICATORS';
      case 2:
        return 'CONFIGURE BOT';
      default:
        return 'CREATE TRADING BOT';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Text(
              _getStepTitle(),
              style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
            ),
            const Spacer(),
            Text(
              'STEP ${_currentStep + 1} OF 3',
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
          ],
        ),
        centerTitle: false,
        leading: IconButton(
          icon: const Icon(LucideIcons.arrowLeft),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // Progress indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 4,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2),
                    color: Colors.grey[200],
                  ),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    width:
                        MediaQuery.of(context).size.width *
                            ((_currentStep + 1) / 3) -
                        32,
                    decoration: BoxDecoration(
                      gradient: AppTheme.primaryGradient,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Step Content
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildStep1CoinSelection(),
                _buildStep2IndicatorSelection(),
                _buildStep3Configuration(),
              ],
            ),
          ),
          // Navigation Buttons
        ],
      ),
      bottomNavigationBar: SafeArea(
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (_currentStep > 0)
                OutlinedButton(
                  onPressed: _previousStep,
                  child: const Text('BACK'),
                )
              else
                Spacer(),
              GradientButton(
                height: 40,
                borderRadius: 50,
                onPressed: _currentStep == 2 ? _createBot : _nextStep,
                text: _currentStep == 2 ? 'CONFIRM' : 'NEXT',
                width: 150,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStep1CoinSelection() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search bar
          Container(
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search coins...',
                prefixIcon: const Icon(LucideIcons.search, size: 20),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(LucideIcons.x, size: 20),
                        onPressed: () {
                          _searchController.clear();
                          _searchCoins('');
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
              ),
              onChanged: _searchCoins,
            ),
          ),

          // Trending Coins Section
          if (_searchController.text.isEmpty) ...[
            const SizedBox(height: 16),
            const Text(
              'Trending Coins',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            if (_isLoadingTrending)
              const Center(child: CircularProgressIndicator())
            else if (_trendingError.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.error_outline, color: Colors.red),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _trendingError,
                        style: TextStyle(color: Colors.red[700]),
                      ),
                    ),
                    TextButton(
                      onPressed: _loadTrendingCoins,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
            else
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.6,
                ),
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemCount: _trendingCoins.length,
                  itemBuilder: (context, index) {
                    final coin = _trendingCoins[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: _buildCoinTile(coin),
                    );
                  },
                ),
              ),
            const SizedBox(height: 8),
          ],
          if (_selectedCoin != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green),
              ),
              child: Row(
                children: [
                  if (_selectedCoin!.image != null)
                    Image.network(
                      _selectedCoin!.image!,
                      width: 32,
                      height: 32,
                      errorBuilder: (context, error, stackTrace) =>
                          const Icon(LucideIcons.coins, size: 32),
                    )
                  else
                    const Icon(LucideIcons.coins, size: 32),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _selectedCoin!.name,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          _selectedCoin!.symbol.toUpperCase(),
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _selectedCoin = null;
                      });
                    },
                    icon: const Icon(LucideIcons.x),
                  ),
                ],
              ),
            ),
          ],
          if (_searchResults.isNotEmpty && _selectedCoin == null) ...[
            const SizedBox(height: 8),
            Container(
              constraints: const BoxConstraints(maxHeight: 300),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ListView.separated(
                shrinkWrap: true,
                padding: const EdgeInsets.symmetric(vertical: 8),
                itemCount: _searchResults.length,
                separatorBuilder: (context, index) => Divider(
                  height: 1,
                  thickness: 1,
                  color: Colors.grey[200],
                  indent: 16,
                  endIndent: 16,
                ),
                itemBuilder: (context, index) {
                  final coin = _searchResults[index];
                  return Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _selectedCoin = coin;
                          _searchResults = [];
                          _searchController.clear();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        child: Row(
                          children: [
                            if (coin.image != null)
                              Image.network(
                                coin.image!,
                                width: 32,
                                height: 32,
                                errorBuilder: (context, error, stackTrace) =>
                                    Container(
                                      width: 32,
                                      height: 32,
                                      decoration: BoxDecoration(
                                        color: Colors.grey[200],
                                        borderRadius: BorderRadius.circular(16),
                                      ),
                                      child: const Icon(
                                        LucideIcons.coins,
                                        size: 16,
                                        color: Colors.grey,
                                      ),
                                    ),
                              )
                            else
                              Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                  color: Colors.grey[200],
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: const Icon(
                                  LucideIcons.coins,
                                  size: 16,
                                  color: Colors.grey,
                                ),
                              ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    coin.name,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 15,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Text(
                                    coin.symbol.toUpperCase(),
                                    style: TextStyle(
                                      fontSize: 13,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Map of indicator display names to their corresponding IndicatorType values
  final Map<String, String> _indicatorNameToType = {
    'RSI': 'rsi',
    'MACD': 'macd',
    'Bollinger Bands': 'bollinger',
    'Moving Average': 'sma', // Default to SMA for Moving Average
    'Stochastic RSI': 'stochastic',
    'Ichimoku Cloud':
        'williams', // This might need adjustment based on your needs
    'ADX': 'adx',
  };

  Widget _buildStep2IndicatorSelection() {
    final indicators = _indicatorNameToType.keys.toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: indicators.length,
            itemBuilder: (context, index) {
              final indicator = indicators[index];
              // Check if the indicator's type is in the selected indicators
              final indicatorType = _indicatorNameToType[indicator];
              final isSelected =
                  indicatorType != null &&
                  _selectedIndicators.contains(indicatorType);

              return Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Material(
                  color: isSelected
                      ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: () {
                      setState(() {
                        if (isSelected) {
                          _selectedIndicators.remove(indicator);
                        } else {
                          // Use the mapped indicator type instead of the display name
                          final indicatorType = _indicatorNameToType[indicator];
                          if (indicatorType != null) {
                            _selectedIndicators.add(indicatorType);
                          }
                        }
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 16,
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              indicator,
                              style: const TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          if (isSelected)
                            Container(
                              width: 24,
                              height: 24,
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.check,
                                size: 16,
                                color: Colors.white,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCoinTile(CoinData coin) {
    final isSelected = _selectedCoin?.id == coin.id;
    final isPositive = coin.priceChange24h != null && coin.priceChange24h! >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;
    final subtitleColor = Theme.of(context).brightness == Brightness.dark
        ? Colors.grey[400]
        : Colors.grey[600];

    return Material(
      color: isSelected
          ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
          : Colors.transparent,
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          setState(() {
            _selectedCoin = coin;
          });
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          child: Row(
            children: [
              // Coin Icon
              if (coin.image != null)
                Image.network(
                  coin.image!,
                  width: 36,
                  height: 36,
                  errorBuilder: (context, error, stackTrace) => Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(18),
                    ),
                    child: const Icon(
                      LucideIcons.coins,
                      size: 18,
                      color: Colors.grey,
                    ),
                  ),
                )
              else
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: const Icon(
                    LucideIcons.coins,
                    size: 18,
                    color: Colors.grey,
                  ),
                ),

              const SizedBox(width: 12),

              // Coin Name and Symbol
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      coin.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 15,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      coin.symbol.toUpperCase(),
                      style: TextStyle(
                        color: subtitleColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

              // Price Change
              if (coin.priceChange24h != null) ...[
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: changeColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isPositive
                            ? LucideIcons.trendingUp
                            : LucideIcons.trendingDown,
                        size: 14,
                        color: changeColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${coin.priceChange24h!.abs().toStringAsFixed(2)}%',
                        style: TextStyle(
                          color: changeColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Selection Indicator
              const SizedBox(width: 8),
              if (isSelected)
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.check, size: 16, color: Colors.white),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIndicatorForm(String indicator) {
    final config = _indicatorConfigs[indicator]!;
    final type = IndicatorType.values.firstWhere((t) => t.name == indicator);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              indicator,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),
            ..._buildIndicatorFields(type, config),
            const SizedBox(height: 8),
            _buildThresholdFields(config, type),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildIndicatorFields(
    IndicatorType type,
    IndicatorConfig config,
  ) {
    switch (type) {
      case IndicatorType.rsi:
        return [
          _buildNumberField(
            'Period',
            config.parameters['period']?.toString() ?? '14',
            (value) => _updateIndicatorConfig(
              config.name,
              config.copyWith(
                parameters: {...config.parameters, 'period': int.parse(value)},
              ),
            ),
            min: 1.0,
            max: 50.0,
          ),
        ];
      case IndicatorType.macd:
        return [
          _buildNumberField(
            'Fast Period',
            config.parameters['fastPeriod']?.toString() ?? '12',
            (value) => _updateIndicatorConfig(
              config.name,
              config.copyWith(
                parameters: {
                  ...config.parameters,
                  'fastPeriod': int.parse(value),
                },
              ),
            ),
            min: 1.0,
            max: 50.0,
          ),
          const SizedBox(height: 12),
          _buildNumberField(
            'Slow Period',
            config.parameters['slowPeriod']?.toString() ?? '26',
            (value) => _updateIndicatorConfig(
              config.name,
              config.copyWith(
                parameters: {
                  ...config.parameters,
                  'slowPeriod': int.parse(value),
                },
              ),
            ),
            min: 1.0,
            max: 50.0,
          ),
          const SizedBox(height: 12),
          _buildNumberField(
            'Signal Period',
            config.parameters['signalPeriod']?.toString() ?? '9',
            (value) => _updateIndicatorConfig(
              config.name,
              config.copyWith(
                parameters: {
                  ...config.parameters,
                  'signalPeriod': int.parse(value),
                },
              ),
            ),
            min: 1.0,
            max: 50.0,
          ),
        ];
      case IndicatorType.bollinger:
        return [
          _buildNumberField(
            'Period',
            config.parameters['period']?.toString() ?? '20',
            (value) => _updateIndicatorConfig(
              config.name,
              config.copyWith(
                parameters: {...config.parameters, 'period': int.parse(value)},
              ),
            ),
            min: 1.0,
            max: 50.0,
          ),
          const SizedBox(height: 12),
          _buildNumberField(
            'Standard Deviations',
            config.parameters['standardDeviations']?.toString() ?? '2',
            (value) => _updateIndicatorConfig(
              config.name,
              config.copyWith(
                parameters: {
                  ...config.parameters,
                  'standardDeviations': double.parse(value),
                },
              ),
            ),
            min: 1.0,
            max: 5.0,
            isDouble: true,
          ),
        ];
      default:
        return [];
    }
  }

  Widget _buildThresholdFields(IndicatorConfig config, IndicatorType type) {
    if (type == IndicatorType.macd) {
      return const SizedBox.shrink(); // MACD doesn't use thresholds
    }

    return Column(
      children: [
        const Divider(),
        const SizedBox(height: 8),
        Text(
          'Trading Signals',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                initialValue: config.buyThreshold.toString(),
                decoration: InputDecoration(
                  labelText: 'Buy Below',
                  suffixText: type == IndicatorType.rsi ? '' : '%',
                  border: const OutlineInputBorder(),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 12,
                  ),
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  final threshold = double.tryParse(value) ?? 0;
                  _updateIndicatorConfig(
                    config.name,
                    config.copyWith(buyThreshold: threshold),
                  );
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                initialValue: config.sellThreshold.toString(),
                decoration: const InputDecoration(
                  labelText: 'Sell Above',
                  suffixText: '%',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 12,
                  ),
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  final threshold = double.tryParse(value) ?? 0;
                  _updateIndicatorConfig(
                    config.name,
                    config.copyWith(sellThreshold: threshold),
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNumberField(
    String label,
    String initialValue,
    ValueChanged<String> onChanged, {
    double min = 1.0,
    double max = 100.0,
    bool isDouble = false,
  }) {
    final controller = TextEditingController(text: initialValue);

    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 12,
        ),
        suffixIcon: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.remove, size: 18),
              onPressed: () {
                final currentValue = isDouble
                    ? double.tryParse(controller.text) ?? min
                    : (int.tryParse(controller.text) ?? min.toInt()).toDouble();
                final newValue = isDouble
                    ? currentValue - 0.5
                    : currentValue - 1;
                if (newValue >= min) {
                  controller.text = isDouble
                      ? newValue.toStringAsFixed(1)
                      : newValue.toInt().toString();
                  onChanged(controller.text);
                }
              },
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
            IconButton(
              icon: const Icon(Icons.add, size: 18),
              onPressed: () {
                final currentValue = isDouble
                    ? double.tryParse(controller.text) ?? min
                    : (int.tryParse(controller.text) ?? min.toInt()).toDouble();
                final newValue = isDouble
                    ? currentValue + 0.5
                    : currentValue + 1;
                if (newValue <= max) {
                  controller.text = isDouble
                      ? newValue.toStringAsFixed(1)
                      : newValue.toInt().toString();
                  onChanged(controller.text);
                }
              },
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
            const SizedBox(width: 8),
          ],
        ),
      ),
      keyboardType: TextInputType.numberWithOptions(decimal: isDouble),
      onChanged: (value) {
        if (value.isNotEmpty) {
          final numValue = isDouble
              ? double.tryParse(value)
              : int.tryParse(value)?.toDouble();
          if (numValue != null && numValue >= min && numValue <= max) {
            onChanged(value);
          }
        }
      },
    );
  }

  void _updateIndicatorConfig(String name, IndicatorConfig newConfig) {
    setState(() {
      _indicatorConfigs[name] = newConfig;
    });
  }

  Widget _buildStep3Configuration() {
    // Auto-generate bot name if not set
    if (_nameController.text.isEmpty && _selectedCoin != null) {
      final indicatorsText = _selectedIndicators.take(2).join(' + ');
      _nameController.text = '${_selectedCoin!.symbol} $indicatorsText Bot';
    }

    // Ensure all selected indicators have configurations
    for (final indicator in _selectedIndicators) {
      if (!_indicatorConfigs.containsKey(indicator)) {
        final type = IndicatorType.values.firstWhere(
          (t) => t.name == indicator,
          orElse: () {
            // If no matching type is found, default to RSI and log an error
            debugPrint('Warning: Unknown indicator type: $indicator');
            return IndicatorType.rsi;
          },
        );
        _indicatorConfigs[indicator] = IndicatorConfig(
          name: indicator,
          parameters: _getDefaultParameters(type),
          buyThreshold: _getDefaultBuyThreshold(type),
          sellThreshold: _getDefaultSellThreshold(type),
        );
      }
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Bot Name
            Card(
              margin: const EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'BOT NAME',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey,
                        letterSpacing: 0.5,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _nameController,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      decoration: const InputDecoration(
                        hintText: 'Enter bot name',
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.zero,
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a bot name';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),

            // Indicator Configurations
            ..._selectedIndicators
                .map((indicator) => _buildIndicatorForm(indicator))
                .toList(),

            // Risk Management
            Card(
              margin: const EdgeInsets.only(top: 8, bottom: 16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'RISK MANAGEMENT',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey,
                        letterSpacing: 0.5,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _investmentController,
                      decoration: InputDecoration(
                        labelText: 'Investment Amount (USDT)',
                        hintText: 'Enter amount to invest',
                        prefixIcon: const Icon(LucideIcons.dollarSign),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter investment amount';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _stopLossController,
                      decoration: const InputDecoration(
                        labelText: 'Stop Loss (%)',
                        hintText: 'Enter stop loss percentage',
                        prefixIcon: Icon(LucideIcons.trendingDown),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter stop loss percentage';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _takeProfitController,
                      decoration: const InputDecoration(
                        labelText: 'Take Profit (%)',
                        hintText: 'Enter take profit percentage',
                        prefixIcon: Icon(LucideIcons.trendingUp),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter take profit percentage';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
