import 'dart:async';
import 'package:dextrip_app/widgets/gradient_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../models/coin_model.dart';
import '../services/dexscreener_service.dart';
import '../widgets/coin_filter_modal.dart';
import 'coin_detail_page.dart';

class CoinPage extends StatefulWidget {
  const CoinPage({
    super.key,
    required this.onThemeToggle,
    required this.isDarkMode,
    required this.isSignedIn,
    required this.onSignIn,
  });

  final VoidCallback onThemeToggle;
  final bool isDarkMode;
  final bool isSignedIn;
  final VoidCallback onSignIn;

  @override
  State<CoinPage> createState() => _CoinPageState();
}

class _CoinPageState extends State<CoinPage> {
  final TextEditingController _searchController = TextEditingController();
  List<CoinData> _coins = [];
  List<CoinData> _filteredCoins = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  String _error = '';
  int _currentPage = 1;
  static const int _pageSize = 25;
  Timer? _debounceTimer;
  Map<String, dynamic> _filters = {};

  @override
  void initState() {
    super.initState();
    _loadTrendingCoins();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadTrendingCoins({bool loadMore = false}) async {
    if (loadMore) {
      setState(() {
        _isLoadingMore = true;
      });
    } else {
      setState(() {
        _isLoading = true;
        _currentPage = 1;
        _coins.clear();
      });
    }

    try {
      final coins = await CoinGeckoService.getTrendingCoins(
        page: _currentPage,
        perPage: _pageSize,
      );

      setState(() {
        if (loadMore) {
          _coins.addAll(coins);
          _isLoadingMore = false;
        } else {
          _coins = coins;
          _isLoading = false;
        }
        _filteredCoins = _coins;
        _currentPage++;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
        _isLoadingMore = false;
      });
    }
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase();
    if (query.isEmpty) {
      setState(() {
        _filteredCoins = _coins;
      });
    } else {
      setState(() {
        _filteredCoins = _coins.where((coin) {
          return coin.name.toLowerCase().contains(query) ||
              coin.symbol.toLowerCase().contains(query) ||
              coin.displayName.toLowerCase().contains(query);
        }).toList();
      });
    }
  }

  Future<void> _searchCoins(String query) async {
    if (query.isEmpty) {
      _loadTrendingCoins();
      return;
    }

    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      final coins = await CoinGeckoService.searchCoins(query);
      setState(() {
        _coins = coins;
        _filteredCoins = coins;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _pasteFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData?.text != null && clipboardData!.text!.isNotEmpty) {
        _searchController.text = clipboardData.text!;
        _searchCoins(clipboardData.text!);
      }
    } catch (e) {
      // Handle clipboard access error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to paste from clipboard'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        title: Row(
          children: [
            const SizedBox(width: 8),
            Image.asset(
              'assets/images/logo.png',
              width: 35,
              height: 35,
              fit: BoxFit.cover,
            ),
            const SizedBox(width: 16),
            const Text(
              'DexTrip',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),

        centerTitle: false,
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: IconButton(
              icon: const Icon(LucideIcons.listFilter),
              onPressed: _showFilterModal,
            ),
          ),
        ],

        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Padding(
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
            child: SizedBox(
              height: 45,
              child: SearchBar(
                controller: _searchController,
                hintText: 'Search Coin Name or Token',
                elevation: const WidgetStatePropertyAll(0),
                backgroundColor: WidgetStatePropertyAll(
                  Colors.blueGrey.withValues(alpha: 0.1),
                ),
                padding: const WidgetStatePropertyAll(
                  EdgeInsets.symmetric(horizontal: 12),
                ),
                constraints: const BoxConstraints(maxHeight: 32),
                leading: const Icon(LucideIcons.search, size: 20),
                trailing: [
                  IconButton(
                    icon: const Icon(LucideIcons.clipboard, size: 20),
                    onPressed: _pasteFromClipboard,
                    tooltip: 'Paste from clipboard',
                  ),
                  if (_searchController.text.isNotEmpty)
                    IconButton(
                      icon: const Icon(LucideIcons.x, size: 20),
                      onPressed: () {
                        _searchController.clear();
                        _loadTrendingCoins();
                      },
                    ),
                ],
                onSubmitted: _searchCoins,
              ),
            ),
          ),
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(LucideIcons.x, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'Error loading coins',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[700],
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _error,
                textAlign: TextAlign.center,
                style: const TextStyle(color: Colors.grey),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadTrendingCoins,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_filteredCoins.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(LucideIcons.search, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No coins found',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Try searching with different keywords',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => _loadTrendingCoins(),
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 8),
        itemCount:
            _filteredCoins.length + (_searchController.text.isEmpty ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _filteredCoins.length &&
              _searchController.text.isEmpty) {
            // Load More button
            return Padding(
              padding: const EdgeInsets.all(6.0),
              child: Center(
                child: _isLoadingMore
                    ? const CircularProgressIndicator()
                    : GradientButton(
                        text: 'Load More',
                        onPressed: () => _loadTrendingCoins(loadMore: true),
                        icon: LucideIcons.plus,
                      ),
              ),
            );
          }
          final coin = _filteredCoins[index];
          return _buildCoinTile(coin);
        },
      ),
    );
  }

  Widget _buildCoinTile(CoinData coin) {
    final isPositive =
        coin.priceChangePercentage24h != null &&
        coin.priceChangePercentage24h! >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;
    final textColor = widget.isDarkMode ? Colors.white : Colors.black;
    final subtitleColor = widget.isDarkMode
        ? Colors.grey[400]
        : Colors.grey[600];

    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  CoinDetailPage(coin: coin, symbol: coin.symbol),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 12),
          child: Row(
            children: [
              // Coin Icon and Rank
              coin.image != null
                  ? Image.network(
                      coin.image!,
                      width: 36,
                      height: 36,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Icon(
                        Icons.currency_bitcoin,
                        color: textColor.withValues(alpha: 0.7),
                      ),
                    )
                  : Center(
                      child: Text(
                        coin.symbol.substring(0, 1).toUpperCase(),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: textColor,
                        ),
                      ),
                    ),
              SizedBox(width: 12),
              // Coin Name and Symbol
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      coin.displayName,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 15,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      coin.displaySymbol,
                      style: TextStyle(
                        color: subtitleColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

              // Price and Change
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    coin.formattedPrice,
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isPositive
                            ? Icons.arrow_drop_up
                            : Icons.arrow_drop_down,
                        color: changeColor,
                        size: 16,
                      ),
                      Text(
                        coin.formattedPriceChange,
                        style: TextStyle(
                          color: changeColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showFilterModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CoinFilterModal(
        currentFilters: _filters,
        onFiltersChanged: (newFilters) {
          setState(() {
            _filters = newFilters;
          });
          _applyFilters();
        },
      ),
    );
  }

  void _applyFilters() {
    // Apply filters to the coin list
    // This is where you would implement the actual filtering logic
    // For now, we'll just show a message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Filters applied: ${_filters.length} active filters'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
