import 'dart:async';
import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../theme/app_theme.dart';
import '../widgets/auth_modal.dart';

class WalletOnboardingPage extends StatefulWidget {
  final VoidCallback? onWalletCreated;

  const WalletOnboardingPage({super.key, this.onWalletCreated});

  @override
  State<WalletOnboardingPage> createState() => _WalletOnboardingPageState();
}

class _WalletOnboardingPageState extends State<WalletOnboardingPage>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  PageController? _pageController;
  bool _agreedToTerms = false;
  int _currentSlide = 0;
  Timer? _autoSlideTimer;

  final List<Map<String, dynamic>> _slides = [
    {
      'title': 'Trade Solana Meme Coins',
      'subtitle':
          'Discover and trade the hottest meme coins on Solana blockchain with real-time data',
      'emoji': '🚀',
      'features': ['PumpFun Integration', 'Raydium DEX', 'Real-time Prices'],
      'gradient': [Color(0xFF6366F1), Color(0xFF8B5CF6)],
    },
    {
      'title': 'Automated Trading Bots',
      'subtitle':
          'Set up intelligent trading bots that work 24/7 to maximize your profits',
      'emoji': '🤖',
      'features': ['Smart Algorithms', 'Risk Management', 'Auto-Execution'],
      'gradient': [Color(0xFF10B981), Color(0xFF059669)],
    },
    {
      'title': 'Real-time Analytics',
      'subtitle':
          'Track your portfolio performance with advanced charts and insights',
      'emoji': '📊',
      'features': ['Live Charts', 'P&L Tracking', 'Market Insights'],
      'gradient': [Color(0xFFF59E0B), Color(0xFFD97706)],
    },
    {
      'title': 'Secure Wallet',
      'subtitle':
          'Your funds are protected with industry-leading security measures',
      'emoji': '�',
      'features': ['Multi-Signature', 'Hardware Support', 'Encrypted Storage'],
      'gradient': [Color(0xFFEF4444), Color(0xFFDC2626)],
    },
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..forward();

    // Auto-slide timer
    _autoSlideTimer = Timer.periodic(const Duration(seconds: 4), (timer) {
      if (mounted && _pageController?.hasClients == true) {
        final nextPage = (_currentSlide + 1) % _slides.length;
        _pageController?.animateToPage(
          nextPage,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _autoSlideTimer?.cancel();
    _fadeController.dispose();
    _pageController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      body: Column(
        children: [
          // Main content
          SizedBox(height: 50),
          // Image slider at the top
          Expanded(
            flex: 2,
            child: _pageController != null
                ? PageView.builder(
                    controller: _pageController!,
                    onPageChanged: (index) {
                      setState(() {
                        _currentSlide = index;
                      });
                    },
                    itemCount: _slides.length,
                    itemBuilder: (context, index) {
                      final slide = _slides[index];
                      final gradient = slide['gradient'] as List<Color>?;
                      final features = slide['features'] as List<String>?;

                      return Container(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors:
                                gradient ??
                                [
                                  AppTheme.primaryColor,
                                  AppTheme.primaryColor.withValues(alpha: 0.8),
                                ],
                          ),
                          borderRadius: BorderRadius.circular(24),
                          boxShadow: [
                            BoxShadow(
                              color: (gradient?.first ?? AppTheme.primaryColor)
                                  .withValues(alpha: 0.3),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(24),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Large emoji icon with background
                              Container(
                                width: 100,
                                height: 100,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(50),
                                ),
                                child: Center(
                                  child: Text(
                                    slide['emoji']!,
                                    style: const TextStyle(fontSize: 50),
                                  ),
                                ),
                              ),

                              const SizedBox(height: 24),

                              // Title
                              Text(
                                slide['title']!,
                                style: const TextStyle(
                                  fontSize: 26,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                                textAlign: TextAlign.center,
                              ),

                              const SizedBox(height: 12),

                              // Subtitle
                              Text(
                                slide['subtitle']!,
                                style: TextStyle(
                                  fontSize: 15,
                                  color: Colors.white.withValues(alpha: 0.9),
                                  height: 1.4,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 3,
                                overflow: TextOverflow.ellipsis,
                              ),

                              const SizedBox(height: 20),

                              // Features list
                              if (features != null) ...[
                                Wrap(
                                  spacing: 8,
                                  runSpacing: 8,
                                  alignment: WrapAlignment.center,
                                  children: features.map((feature) {
                                    return Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 6,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withValues(
                                          alpha: 0.2,
                                        ),
                                        borderRadius: BorderRadius.circular(16),
                                        border: Border.all(
                                          color: Colors.white.withValues(
                                            alpha: 0.3,
                                          ),
                                        ),
                                      ),
                                      child: Text(
                                        feature,
                                        style: const TextStyle(
                                          fontSize: 11,
                                          color: Colors.white,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ],
                            ],
                          ),
                        ),
                      );
                    },
                  )
                : const Center(child: CircularProgressIndicator()),
          ),

          // Slide indicators
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(_slides.length, (index) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                width: index == _currentSlide ? 24 : 8,
                height: 8,
                decoration: BoxDecoration(
                  color: index == _currentSlide
                      ? AppTheme.primaryColor
                      : Colors.white24,
                  borderRadius: BorderRadius.circular(4),
                ),
              );
            }),
          ),

          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Terms agreement
                  FadeTransition(
                    opacity: _fadeController,
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _agreedToTerms = !_agreedToTerms;
                        });
                      },
                      child: Row(
                        children: [
                          Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: _agreedToTerms
                                  ? AppTheme.primaryColor
                                  : Colors.transparent,
                              border: Border.all(
                                color: _agreedToTerms
                                    ? AppTheme.primaryColor
                                    : Colors.white54,
                                width: 2,
                              ),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: _agreedToTerms
                                ? const Icon(
                                    LucideIcons.check,
                                    color: Colors.white,
                                    size: 16,
                                  )
                                : null,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: RichText(
                              text: TextSpan(
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.white70,
                                ),
                                children: [
                                  const TextSpan(text: 'I agree to the '),
                                  TextSpan(
                                    text: 'Terms of Service',
                                    style: TextStyle(
                                      color: AppTheme.primaryColor,
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Create wallet button
                  FadeTransition(
                    opacity: _fadeController,
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _agreedToTerms
                            ? _showCreateWalletOptions
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        child: const Text(
                          'Create a new wallet',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Import wallet button
                  // FadeTransition(
                  //   opacity: _fadeController,
                  //   child: TextButton(
                  //     onPressed: _agreedToTerms ? _showSignInOptions : null,
                  //     child: Text(
                  //       'I already have a wallet',
                  //       style: TextStyle(
                  //         fontSize: 16,
                  //         color: _agreedToTerms ? Colors.white : Colors.white38,
                  //         fontWeight: FontWeight.w500,
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showCreateWalletOptions() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AuthModal(
        onSuccess: () {
          widget.onWalletCreated?.call();
        },
        isSignIn: false,
      ),
    );
  }

  // void _showSignInOptions() {
  //   showModalBottomSheet(
  //     context: context,
  //     isScrollControlled: true,
  //     backgroundColor: Colors.transparent,
  //     builder: (context) => AuthModal(
  //       onSuccess: () {
  //         widget.onWalletCreated?.call();
  //       },
  //       isSignIn: true,
  //     ),
  //   );
  // }

  // void _showHelpDialog() {
  //   showDialog(
  //     context: context,
  //     builder: (context) => AlertDialog(
  //       backgroundColor: const Color(0xFF2A2A2A),
  //       title: const Text('Need Help?', style: TextStyle(color: Colors.white)),
  //       content: const Text(
  //         'DexTrip is a secure trading platform for Solana. Create a wallet to start trading meme coins and managing your portfolio.',
  //         style: TextStyle(color: Colors.white70),
  //       ),
  //       actions: [
  //         TextButton(
  //           onPressed: () => Navigator.pop(context),
  //           child: Text(
  //             'Got it',
  //             style: TextStyle(color: AppTheme.primaryColor),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }
}
